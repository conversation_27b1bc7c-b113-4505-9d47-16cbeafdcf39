# Snapback License Management API - Swift macOS Integration Guide

## Table of Contents

1. [Overview](#overview)
2. [Environment Setup](#environment-setup)
3. [Authentication & Device Registration](#authentication--device-registration)
4. [API Endpoints](#api-endpoints)
5. [Swift Integration Examples](#swift-integration-examples)
6. [Error Handling](#error-handling)
7. [Rate Limiting & Security](#rate-limiting--security)
8. [Payment Integration](#payment-integration)
9. [Troubleshooting](#troubleshooting)

## Overview

The Snapback License Management API provides a complete solution for managing software licenses and device registration. This guide focuses on integrating the API with Swift macOS applications.

### License Types

- **Trial**: Free, 1 device, 14 days
- **Standard**: $4.99, 2 devices, lifetime
- **Extended**: $9.99, 5 devices, lifetime

### License Key Format

License keys are 24-character alphanumeric strings using a secure character set that excludes confusing characters (0, O, 1, I, L):

- **Character Set**: `ABCDEFGHJKMNPQRSTUVWXYZ23456789` (30 characters)
- **Length**: 24 characters
- **Display Format**: `XXXX-XXXX-XXXX-XXXX-XXXX-XXXX` (with dashes for readability)
- **API Format**: `XXXXXXXXXXXXXXXXXXXXXXXX` (normalized, no dashes)

**Example:**
- Display: `ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2`
- API: `ABCDEFGHJKMNPQRSTUVWXYZ2`

**Important:** When sending license keys to the API, always normalize them by removing dashes. When displaying to users, format with dashes every 4 characters for better readability.

### Base URL

```
Production: https://your-domain.com/api
Development: http://localhost:3000/api
```

## Environment Setup

### Required Environment Variables

```bash
# Server Configuration
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:password@localhost:5432/snapback

# CORS Configuration
CORS_ORIGIN=https://your-app-domain.com

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters-long

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Security Configuration (Optional)
BLOCKED_IPS=*************,*********
REQUEST_SIGNING_SECRET=your-request-signing-secret

# Rate Limiting Configuration (Optional)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration (Optional)
LOG_LEVEL=info
```

## Authentication & Device Registration

### Device ID Generation

Generate a unique device identifier in your Swift app:

```swift
import Foundation
import CryptoKit

func generateDeviceID() -> String {
    let systemInfo = [
        ProcessInfo.processInfo.hostName,
        ProcessInfo.processInfo.operatingSystemVersionString,
        // Add more system-specific identifiers as needed
    ].joined(separator: "-")

    let data = Data(systemInfo.utf8)
    let hash = SHA256.hash(data: data)
    return hash.compactMap { String(format: "%02x", $0) }.joined()
}

// License key formatting utilities
func formatLicenseKeyForDisplay(_ licenseKey: String) -> String {
    let normalized = licenseKey.replacingOccurrences(of: "-", with: "")
    var formatted = ""
    for (index, character) in normalized.enumerated() {
        if index > 0 && index % 4 == 0 {
            formatted += "-"
        }
        formatted += String(character)
    }
    return formatted
}

func normalizeLicenseKeyForAPI(_ licenseKey: String) -> String {
    return licenseKey.replacingOccurrences(of: "-", with: "").uppercased()
}
```

### JWT Token Management

After successful license validation, store and manage the device token:

```swift
class TokenManager {
    private let keychain = Keychain(service: "com.yourapp.snapback")

    func storeDeviceToken(_ token: String) {
        keychain["device_token"] = token
    }

    func getDeviceToken() -> String? {
        return keychain["device_token"]
    }

    func clearDeviceToken() {
        keychain["device_token"] = nil
    }
}
```

## API Endpoints

### Payment Endpoints

#### 1. Get Pricing Information

**Endpoint:** `GET /payments/pricing`

**Description:** Get current pricing for all license types (for display purposes only - actual payment processing occurs on external website)

**Response (200 OK):**

```json
{
  "trial": {
    "price": 0,
    "maxDevices": 1,
    "duration": "14 days"
  },
  "standard": {
    "price": 499,
    "maxDevices": 2,
    "duration": "Lifetime"
  },
  "extended": {
    "price": 999,
    "maxDevices": 5,
    "duration": "Lifetime"
  },
  "additionalDevice": {
    "price": 99,
    "description": "Per additional device"
  }
}
```

**Note:** Payment processing for paid licenses occurs on the external marketing website. Users receive license keys via email after purchase and manually enter them into the app for validation.

### License Endpoints

#### 2. Create License (Trial Creation Only)

**Endpoint:** `POST /licenses/create`

**Description:** Creates a new trial license. Paid licenses are processed externally on the marketing website.

**Headers:**

```
Content-Type: application/json
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "licenseType": "trial",
  "deviceId": "optional-device-id"
}
```

**Important Notes:**
- **Trial licenses only** - This endpoint is only used for creating trial licenses. Paid licenses are processed externally.
- **deviceId is required for trial licenses** - Trial licenses must include a deviceId for immediate device registration to prevent abuse
- **License key format** - License keys are now 24-character alphanumeric strings (uppercase letters and numbers, excluding confusing characters like 0, O, 1, I, L)

**Response (201 Created):**

```json
{
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "licenseType": "standard",
  "email": "<EMAIL>",
  "maxDevices": 2,
  "expiresAt": null,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

#### Trial License Restrictions

**Important:** To prevent abuse, the system enforces restrictions on trial license creation:

**Device ID Requirement:**
- **Trial licenses MUST include a deviceId** in the request body
- This enables immediate device registration and prevents abuse
- Attempting to create a trial license without deviceId returns `VALIDATION_ERROR`

**Email-based Restriction:**
- Each email address can only be used for one trial license
- Attempting to create a second trial with the same email returns `TRIAL_ALREADY_USED_EMAIL`

**Device-based Restriction:**
- Each device can only be used for one trial license
- Attempting to create a second trial on the same device returns `TRIAL_ALREADY_USED_DEVICE`

**Combined Restriction:**
- If both email and device have been used for trials, returns `TRIAL_ALREADY_USED`

**Trial-to-Paid Upgrade Path:**
- Users can seamlessly upgrade from trial to paid licenses without restriction
- When a user with an existing trial license purchases a paid license, the system:
  1. **Upgrades the existing trial license** instead of creating a new one
  2. **Preserves the original license key** for continuity
  3. **Maintains all registered devices** from the trial
  4. **Removes the expiration date** (converts to lifetime license)
  5. **Increases the device limit** based on the purchased license type
  6. **Logs the upgrade** for audit purposes

**Example Upgrade Flow:**
1. User creates trial license: `trial_abc123` (1 device, 14-day expiration)
2. User purchases standard license with same email
3. System upgrades `trial_abc123` to standard (2 devices, no expiration)
4. User continues using the same license key seamlessly

**Best Practices:**
- Always handle trial restriction errors gracefully
- Provide clear messaging to users about trial limitations
- Offer upgrade paths to paid licenses when trial restrictions are encountered
- Consider implementing user-friendly error messages that guide users toward purchasing
- When users have existing trials, guide them to purchase rather than create new trials

**Example Error Response:**

```json
{
  "error": "Trial license already used",
  "code": "TRIAL_ALREADY_USED_EMAIL",
  "details": "A trial license has already been used with this email address. Each email can only be used for one trial license.",
  "metadata": {
    "email": "<EMAIL>"
  }
}
```

#### License Activation Flows

The system supports two distinct license activation flows:

**Trial License Flow:**
1. User requests trial license in app
2. App calls `/licenses/create` with `deviceId` included
3. License created and device registered immediately
4. User can start using the app right away

**Paid License Flow:**
1. User purchases license on external marketing website
2. License key sent to user via email after payment
3. User enters license key in app
4. App calls `/licenses/validate` which registers the device
5. User can start using the app

This dual-flow system ensures optimal user experience for both trial and paid scenarios.

#### 7. Validate License & Register Device

**Endpoint:** `POST /licenses/validate`

**Description:** Validates a license key and registers the device (for paid licenses) or validates existing device registration (for trial licenses)

**Headers:**

```
Content-Type: application/json
```

**Request Body:**

```json
{
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "deviceId": "abc123def456...",
  "appVersion": "1.0.0"
}
```

**Response (200 OK):**

```json
{
  "valid": true,
  "deviceToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "licenseType": "standard",
  "expiresAt": null,
  "devicesUsed": 1,
  "maxDevices": 2
}
```

#### 8. Get License Status

**Endpoint:** `GET /licenses/status/{licenseKey}`

**Description:** Get detailed status information for a license

**Response (200 OK):**

```json
{
  "licenseKey": "ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2",
  "licenseType": "standard",
  "email": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00Z",
  "expiresAt": null,
  "maxDevices": 2,
  "devicesUsed": 1,
  "isExpired": false,
  "isActive": true,
  "devices": [
    {
      "id": "device_123",
      "firstSeen": "2024-01-15T10:35:00Z",
      "lastSeen": "2024-01-15T14:20:00Z",
      "appVersion": "1.0.0",
      "isActive": true
    }
  ]
}
```

#### 9. Remove Device

**Endpoint:** `DELETE /licenses/devices/{deviceId}`

**Description:** Remove a device from a license

**Headers:**

```
Authorization: Bearer {deviceToken}
```

**Response (200 OK):**

```json
{
  "message": "Device removed successfully",
  "devicesRemaining": 1
}
```

#### 10. Resend License Email

**Endpoint:** `POST /licenses/resend`

**Description:** Resend license information to email

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**

```json
{
  "message": "License information sent successfully"
}
```

## Swift Integration Examples

### Complete License Manager Class

```swift
import Foundation
import Combine

class LicenseManager: ObservableObject {
    @Published var isLicensed = false
    @Published var licenseInfo: LicenseInfo?
    @Published var errorMessage: String?

    private let baseURL = "https://your-domain.com/api"
    private let tokenManager = TokenManager()
    private var cancellables = Set<AnyCancellable>()

    struct LicenseInfo {
        let licenseKey: String
        let licenseType: String
        let expiresAt: Date?
        let devicesUsed: Int
        let maxDevices: Int
    }

    func validateLicense(licenseKey: String) {
        let deviceId = generateDeviceID()
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"

        // Normalize license key for API (remove dashes)
        let normalizedLicenseKey = normalizeLicenseKeyForAPI(licenseKey)

        let request = LicenseValidationRequest(
            licenseKey: normalizedLicenseKey,
            deviceId: deviceId,
            appVersion: appVersion
        )

        performRequest(
            endpoint: "/licenses/validate",
            method: "POST",
            body: request
        )
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error)
                }
            },
            receiveValue: { [weak self] (response: LicenseValidationResponse) in
                if response.valid {
                    self?.tokenManager.storeDeviceToken(response.deviceToken)
                    self?.isLicensed = true
                    self?.licenseInfo = LicenseInfo(
                        licenseKey: formatLicenseKeyForDisplay(normalizedLicenseKey),
                        licenseType: response.licenseType,
                        expiresAt: response.expiresAt,
                        devicesUsed: response.devicesUsed,
                        maxDevices: response.maxDevices
                    )
                } else {
                    self?.errorMessage = "Invalid license key"
                }
            }
        )
        .store(in: &cancellables)
    }

    private func performRequest<T: Codable, U: Codable>(
        endpoint: String,
        method: String,
        body: T? = nil
    ) -> AnyPublisher<U, Error> {
        guard let url = URL(string: baseURL + endpoint) else {
            return Fail(error: URLError(.badURL))
                .eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        if let body = body {
            do {
                request.httpBody = try JSONEncoder().encode(body)
            } catch {
                return Fail(error: error)
                    .eraseToAnyPublisher()
            }
        }

        return URLSession.shared.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: U.self, decoder: JSONDecoder())
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    private func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.errorMessage = error.localizedDescription
        }
    }
}
```

### Request/Response Models

```swift
// License Request Models
struct LicenseValidationRequest: Codable {
    let licenseKey: String
    let deviceId: String
    let appVersion: String
}

struct LicenseCreationRequest: Codable {
    let email: String
    let licenseType: String
    let deviceId: String?
}

struct ResendLicenseRequest: Codable {
    let email: String
}

struct PricingResponse: Codable {
    let trial: PricingTier
    let standard: PricingTier
    let extended: PricingTier
    let additionalDevice: AdditionalDevicePricing
}

struct PricingTier: Codable {
    let price: Int
    let maxDevices: Int
    let duration: String
}

struct AdditionalDevicePricing: Codable {
    let price: Int
    let description: String
}

// License Response Models
struct LicenseValidationResponse: Codable {
    let valid: Bool
    let deviceToken: String?
    let licenseType: String?
    let expiresAt: Date?
    let devicesUsed: Int?
    let maxDevices: Int?
}

struct LicenseCreationResponse: Codable {
    let licenseKey: String
    let licenseType: String
    let email: String
    let maxDevices: Int
    let expiresAt: Date?
    let createdAt: Date
}

struct LicenseStatusResponse: Codable {
    let licenseKey: String
    let licenseType: String
    let email: String
    let createdAt: Date
    let expiresAt: Date?
    let maxDevices: Int
    let devicesUsed: Int
    let isExpired: Bool
    let isActive: Bool
    let devices: [DeviceInfo]
}

struct DeviceInfo: Codable {
    let id: String
    let firstSeen: Date
    let lastSeen: Date
    let appVersion: String
    let isActive: Bool
}

struct ErrorResponse: Codable {
    let error: String
    let code: String?
    let details: String?
    let retryAfter: Int?
}
```

### Trial License Creation with Restriction Handling

```swift
class TrialLicenseManager: ObservableObject {
    @Published var creationState: CreationState = .idle
    @Published var errorMessage: String?

    enum CreationState {
        case idle
        case creating
        case success(LicenseCreationResponse)
        case failed(LicenseError)
    }

    func createTrialLicense(email: String) async {
        await MainActor.run {
            self.creationState = .creating
            self.errorMessage = nil
        }

        let deviceId = generateDeviceID()
        let request = LicenseCreationRequest(
            email: email,
            licenseType: "trial",
            deviceId: deviceId,
            stripePaymentIntentId: nil
        )

        do {
            let response: LicenseCreationResponse = try await networkService.request(
                endpoint: "/licenses/create",
                method: .POST,
                body: request
            )

            await MainActor.run {
                self.creationState = .success(response)
            }

        } catch let error as NetworkError {
            let licenseError = mapToLicenseError(error)

            await MainActor.run {
                self.creationState = .failed(licenseError)
                self.errorMessage = licenseError.errorDescription
            }
        }
    }

    private func mapToLicenseError(_ networkError: NetworkError) -> LicenseError {
        guard case .apiError(let code, let message, _) = networkError else {
            return .networkError(networkError)
        }

        switch code {
        case "TRIAL_ALREADY_USED_EMAIL":
            return .trialAlreadyUsedEmail
        case "TRIAL_ALREADY_USED_DEVICE":
            return .trialAlreadyUsedDevice
        case "TRIAL_ALREADY_USED":
            return .trialAlreadyUsed
        case "VALIDATION_ERROR":
            return .validationFailed(message)
        case "RATE_LIMIT_EXCEEDED":
            return .rateLimited(retryAfter: 60)
        default:
            return .networkError(networkError)
        }
    }
}

// Usage in SwiftUI
struct TrialLicenseView: View {
    @StateObject private var trialManager = TrialLicenseManager()
    @State private var email = ""
    @State private var showingUpgradeOptions = false

    var body: some View {
        VStack(spacing: 20) {
            TextField("Email Address", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())

            Button("Start Free Trial") {
                Task {
                    await trialManager.createTrialLicense(email: email)
                }
            }
            .disabled(email.isEmpty || trialManager.creationState == .creating)

            switch trialManager.creationState {
            case .idle:
                EmptyView()

            case .creating:
                ProgressView("Creating trial license...")

            case .success(let license):
                VStack {
                    Text("Trial license created successfully!")
                        .foregroundColor(.green)
                    Text("License Key: \(license.licenseKey)")
                        .font(.monospaced(.body)())
                }

            case .failed(let error):
                VStack {
                    Text(error.errorDescription ?? "Unknown error")
                        .foregroundColor(.red)

                    if case .trialAlreadyUsedEmail = error,
                       case .trialAlreadyUsedDevice = error,
                       case .trialAlreadyUsed = error {
                        Button("Upgrade to Paid License") {
                            showingUpgradeOptions = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
            }
        }
        .padding()
        .sheet(isPresented: $showingUpgradeOptions) {
            PurchaseLicenseView(email: email)
        }
    }
}
```

### Trial-to-Paid License Upgrades

The system automatically handles trial-to-paid license upgrades when users purchase a paid license with the same email address as their existing trial. Here's how to implement this in your Swift app:

```swift
class LicenseUpgradeManager: ObservableObject {
    @Published var upgradeState: UpgradeState = .idle
    @Published var currentLicense: LicenseInfo?

    enum UpgradeState {
        case idle
        case upgrading
        case success(LicenseInfo)
        case failed(Error)
    }

    func validatePaidLicense(licenseKey: String) async {
        await MainActor.run {
            upgradeState = .upgrading
        }

        do {
            let deviceId = await DeviceIdentifier.getDeviceId()

            let requestBody: [String: Any] = [
                "licenseKey": licenseKey,
                "deviceId": deviceId
            ]

            let response = try await APIClient.shared.createLicense(requestBody)

            await MainActor.run {
                // The response will indicate if this was an upgrade
                if let message = response["message"] as? String,
                   message.contains("upgraded") {
                    print("Successfully upgraded trial license to \(licenseType)")
                } else {
                    print("Created new \(licenseType) license")
                }

                let licenseInfo = LicenseInfo(
                    licenseKey: response["licenseKey"] as! String,
                    licenseType: response["licenseType"] as! String,
                    maxDevices: response["maxDevices"] as! Int,
                    expiresAt: response["expiresAt"] as? String,
                    devicesUsed: response["devicesUsed"] as! Int
                )

                upgradeState = .success(licenseInfo)
                currentLicense = licenseInfo
            }
        } catch {
            await MainActor.run {
                upgradeState = .failed(error)
            }
        }
    }
}
```

**Key Benefits of the Upgrade System:**
- **Seamless Experience**: Users keep their existing license key
- **Device Continuity**: All registered devices remain active
- **No Data Loss**: Trial usage history is preserved
- **Automatic Conversion**: No manual intervention required

**Example Usage in SwiftUI:**

```swift
struct UpgradeView: View {
    @StateObject private var upgradeManager = LicenseUpgradeManager()
    let trialEmail: String

    var body: some View {
        VStack(spacing: 20) {
            Text("Upgrade Your Trial License")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Your trial will be upgraded to a full license with the same license key.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)

            Button("Enter License Key") {
                Task {
                    // User enters license key purchased from external website
                    await upgradeManager.validatePaidLicense(
                        licenseKey: "SNAP-BACK-2025-LIFE"
                    )
                }
            }
            .buttonStyle(.borderedProminent)

            switch upgradeManager.upgradeState {
            case .upgrading:
                ProgressView("Upgrading license...")
            case .success(let license):
                VStack {
                    Text("✅ License upgraded successfully!")
                        .foregroundColor(.green)
                    Text("Same license key: \(license.licenseKey)")
                        .font(.monospaced(.caption))
                }
            case .failed(let error):
                Text("Upgrade failed: \(error.localizedDescription)")
                    .foregroundColor(.red)
            case .idle:
                EmptyView()
            }
        }
        .padding()
    }
}
```

## Error Handling

### Standard Error Response Format

All API errors follow this format:

```json
{
  "error": "Human-readable error message",
  "code": "ERROR_CODE",
  "details": "Additional error details",
  "retryAfter": 60
}
```

### Error Codes

| Code                    | Description                        | HTTP Status |
| ----------------------- | ---------------------------------- | ----------- |
| `VALIDATION_ERROR`      | Request validation failed          | 400         |
| `UNAUTHORIZED`          | Missing or invalid authentication  | 401         |
| `INVALID_TOKEN`         | Device token is invalid or expired | 401         |
| `NOT_FOUND`             | Resource not found                 | 404         |
| `LICENSE_NOT_FOUND`     | License key not found              | 404         |
| `DEVICE_NOT_REGISTERED` | Device not registered with license | 404         |
| `SESSION_NOT_FOUND`     | Checkout session not found        | 404         |
| `PAYMENT_INTENT_NOT_FOUND` | Payment intent not found        | 404         |
| `LICENSE_EXPIRED`       | License has expired                | 410         |
| `MAX_DEVICES_REACHED`   | Maximum devices limit reached      | 409         |
| `TRIAL_ALREADY_USED_EMAIL` | Trial license already used with this email | 409 |
| `TRIAL_ALREADY_USED_DEVICE` | Trial license already used on this device | 409 |
| `TRIAL_ALREADY_USED`    | Trial license already used (email and device) | 409 |
| `PAYMENT_REQUIRED`      | Payment verification failed        | 402         |
| `PAYMENT_FAILED`        | Payment processing failed          | 402         |
| `PAYMENT_INCOMPLETE`    | Payment not yet completed          | 402         |
| `CHECKOUT_SESSION_EXPIRED` | Checkout session has expired    | 410         |
| `RATE_LIMIT_EXCEEDED`   | Too many requests                  | 429         |
| `INTERNAL_ERROR`        | Server internal error              | 500         |

### Swift Error Handling Example

```swift
enum LicenseError: LocalizedError {
    case validationFailed(String)
    case rateLimited(retryAfter: Int)
    case unauthorized
    case maxDevicesReached
    case licenseExpired
    case trialAlreadyUsedEmail
    case trialAlreadyUsedDevice
    case trialAlreadyUsed
    case paymentRequired
    case paymentFailed(String)
    case paymentIncomplete
    case sessionNotFound
    case sessionExpired
    case networkError(Error)

    var errorDescription: String? {
        switch self {
        case .validationFailed(let message):
            return "Validation failed: \(message)"
        case .rateLimited(let retryAfter):
            return "Too many requests. Please wait \(retryAfter) seconds."
        case .unauthorized:
            return "Authentication failed. Please re-validate your license."
        case .maxDevicesReached:
            return "Maximum number of devices reached for this license."
        case .licenseExpired:
            return "License has expired. Please renew your license."
        case .trialAlreadyUsedEmail:
            return "A trial license has already been used with this email address."
        case .trialAlreadyUsedDevice:
            return "A trial license has already been used on this device."
        case .trialAlreadyUsed:
            return "A trial license has already been used with this email and device."
        case .paymentRequired:
            return "Payment is required to create this license."
        case .paymentFailed(let message):
            return "Payment failed: \(message)"
        case .paymentIncomplete:
            return "Payment is still being processed. Please wait."
        case .sessionNotFound:
            return "Payment session not found. Please try again."
        case .sessionExpired:
            return "Payment session has expired. Please start a new payment."
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

func handleLicenseValidation() async {
    do {
        let response: LicenseValidationResponse = try await networkService.request(
            endpoint: "/licenses/validate",
            method: .POST,
            body: validationRequest
        )

        await MainActor.run {
            self.isLicensed = response.valid
        }

    } catch NetworkError.badRequest(let message) {
        throw LicenseError.validationFailed(message)

    } catch NetworkError.rateLimited(let retryAfter) {
        throw LicenseError.rateLimited(retryAfter: retryAfter)

    } catch NetworkError.unauthorized {
        throw LicenseError.unauthorized

    } catch {
        throw LicenseError.networkError(error)
    }
}
```

## Rate Limiting & Security

### Rate Limits

| Endpoint             | Limit        | Window     |
| -------------------- | ------------ | ---------- |
| `/licenses/validate` | 10 requests  | 15 minutes |
| `/licenses/create`   | 5 requests   | 15 minutes |
| `/licenses/resend`   | 3 requests   | 15 minutes |
| General API          | 100 requests | 15 minutes |

### Security Headers

Include these headers in your requests:

```swift
// User Agent identification
request.setValue("SnapbackApp/1.0 (macOS)", forHTTPHeaderField: "User-Agent")

// Device token for authenticated requests
if let token = tokenManager.getDeviceToken() {
    request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
}
```

### Request Signing (Optional)

For enhanced security, implement request signing:

```swift
import CryptoKit

func signRequest(_ request: inout URLRequest, secret: String) {
    let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
    let body = request.httpBody.map { String(data: $0, encoding: .utf8) ?? "" } ?? ""
    let payload = timestamp + body

    let key = SymmetricKey(data: Data(secret.utf8))
    let signature = HMAC<SHA256>.authenticationCode(for: Data(payload.utf8), using: key)
    let signatureHex = Data(signature).map { String(format: "%02x", $0) }.joined()

    request.setValue(signatureHex, forHTTPHeaderField: "X-Request-Signature")
    request.setValue(timestamp, forHTTPHeaderField: "X-Request-Timestamp")
}
```

## Payment Integration

The license system supports a streamlined architecture with external payment processing:

### Payment Flow Architecture

**Important:** Payment processing for paid licenses occurs on the external marketing website (out of scope for this app). Users receive license keys via email after purchase and manually enter them into the app for validation.

The app only handles:
- **Trial license creation** through the `/licenses/create` endpoint
- **License validation** for both trial and paid licenses through the `/licenses/validate` endpoint
- **Pricing display** through the `/payments/pricing` endpoint

### Swift Trial License Integration Example

```swift
class TrialManager: ObservableObject {
    @Published var trialState: TrialState = .idle

    enum TrialState {
        case idle
        case creating
        case completed(licenseKey: String)
        case failed(Error)
    }


    func createTrialLicense(email: String, deviceId: String? = nil) async {
        await MainActor.run {
            self.trialState = .creating
        }

        do {
            let request = LicenseCreationRequest(
                email: email,
                licenseType: "trial",
                deviceId: deviceId ?? getDeviceIdentifier()
            )

            let response: LicenseCreationResponse = try await networkService.request(
                endpoint: "/licenses/create",
                method: .POST,
                body: request
            )

            await MainActor.run {
                self.trialState = .completed(licenseKey: response.licenseKey)
            }

        } catch {
            await MainActor.run {
                self.trialState = .failed(error)
            }
        }
    }

}
```

## License Validation Integration

```swift
class LicenseManager: ObservableObject {
    @Published var licenseState: LicenseState = .checking

    enum LicenseState {
        case checking
        case valid(LicenseInfo)
        case invalid(String)
        case expired
        case deviceLimitReached
    }

    func validateLicense(_ licenseKey: String) async {
        await MainActor.run {
            self.licenseState = .checking
        }

        do {
            let request = LicenseValidationRequest(
                licenseKey: licenseKey,
                deviceId: getDeviceIdentifier(),
                appVersion: getAppVersion()
            )

            let response: LicenseValidationResponse = try await networkService.request(
                endpoint: "/licenses/validate",
                method: .POST,
                body: request
            )

            await MainActor.run {
                if response.valid {
                    let licenseInfo = LicenseInfo(
                        licenseKey: licenseKey,
                        licenseType: response.licenseType ?? "unknown",
                        maxDevices: response.maxDevices ?? 1,
                        expiresAt: response.expiresAt,
                        email: response.email
                    )
                    self.licenseState = .valid(licenseInfo)
                } else {
                    self.licenseState = .invalid(response.message ?? "Invalid license")
                }
            }

        } catch {
            await MainActor.run {
                if let apiError = error as? APIError {
                    switch apiError {
                    case .licenseExpired:
                        self.licenseState = .expired
                    case .maxDevicesReached:
                        self.licenseState = .deviceLimitReached
                    default:
                        self.licenseState = .invalid(apiError.localizedDescription)
                    }
                } else {
                    self.licenseState = .invalid(error.localizedDescription)
                }
            }
        }
    }
}
```

## SwiftUI Integration Example

```swift
struct LicenseView: View {
    @StateObject private var licenseManager = LicenseManager()
    @StateObject private var trialManager = TrialManager()
    @State private var licenseKey = ""
    @State private var email = ""

    var body: some View {
        VStack(spacing: 20) {
            switch licenseManager.licenseState {
            case .checking:
                ProgressView("Validating license...")

            case .valid(let licenseInfo):
                VStack {
                    Text("✅ License Valid")
                        .foregroundColor(.green)
                    Text("Type: \(licenseInfo.licenseType)")
                    Text("Devices: \(licenseInfo.maxDevices)")
                }

            case .invalid(let message):
                VStack {
                    Text("❌ Invalid License")
                        .foregroundColor(.red)
                    Text(message)
                        .font(.caption)
                }

            case .expired:
                Text("⏰ License Expired")
                    .foregroundColor(.orange)

            case .deviceLimitReached:
                Text("📱 Device Limit Reached")
                    .foregroundColor(.orange)
            }

            TextField("License Key", text: $licenseKey)
                .textFieldStyle(RoundedBorderTextFieldStyle())

            Button("Validate License") {
                Task {
                    await licenseManager.validateLicense(licenseKey)
                }
            }
            .disabled(licenseKey.isEmpty)

            Divider()

            TextField("Email", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())

            Button("Start Trial") {
                Task {
                    await trialManager.createTrialLicense(email: email)
                }
            }
            .disabled(email.isEmpty)

            switch trialManager.trialState {
            case .idle:
                EmptyView()
            case .creating:
                ProgressView("Creating trial license...")
            case .completed(let licenseKey):
                Text("✅ Trial Created!")
                    .foregroundColor(.green)
                Text("License: \(licenseKey)")
                    .font(.caption)
            case .failed(let error):
                Text("❌ Trial Creation Failed")
                    .foregroundColor(.red)
                Text(error.localizedDescription)
                    .font(.caption)
            }
        }
        .padding()
    }
}
```
