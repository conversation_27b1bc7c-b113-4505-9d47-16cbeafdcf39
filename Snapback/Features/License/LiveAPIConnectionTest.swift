import Foundation

/// Quick test to verify live API connection and troubleshoot database connection issues
/// Note: LicenseAPIService is a singleton that initializes its network manager at startup,
/// so environment changes require an app restart to take effect.
class LiveAPIConnectionTest {

    private let configuration = LicenseAPIConfiguration.shared

    /// Check current API configuration and test connection
    func runConnectionTest() async {
        print("=== Snapback Live API Connection Test ===")
        print("")

        // 1. Show current configuration
        await showCurrentConfiguration()

        // 2. Test current endpoint
        await testCurrentEndpoint()

        // 3. Show how to connect to development API
        await showDevelopmentInstructions()

        print("=== Connection Test Complete ===")
    }

    /// Show current API configuration
    private func showCurrentConfiguration() async {
        print("📋 Current Configuration:")
        print("  Environment: \(configuration.currentEnvironment.displayName)")
        print("  Base URL: \(configuration.baseURL)")
        print("  Development URL: http://localhost:3000/api")
        print("")
    }

    /// Test current endpoint
    private func testCurrentEndpoint() async {
        print("🔍 Testing Current Endpoint...")

        do {
            let pricing = try await LicenseAPIService.shared.getPricing()
            print("✅ Current endpoint successful")
            print(
                "  Response: Trial $\(pricing.trial.price/100), Standard $\(pricing.standard.price/100)"
            )
            print("  🚀 This is REAL data from your API server!")
        } catch {
            print("❌ Current endpoint failed: \(error.localizedDescription)")
            if let apiError = error as? LicenseAPIError {
                print("  Error Type: \(apiError)")
                print("  HTTP Status: \(apiError.httpStatusCode)")
            }
        }
        print("")
    }

    /// Show instructions for connecting to development API
    private func showDevelopmentInstructions() async {
        print("🚀 How to Connect to Your Live API Server:")
        print("")

        print("📋 Current Status:")
        print("  ✅ Currently using REAL API server")
        print("  🚀 Data should be saving to your database!")
        print("")

        print("🔧 To Connect to Live API Server:")
        print("  1. ✅ Default environment changed to .development")
        print("  2. 🔄 RESTART the Snapback app (important!)")
        print("  3. 🚀 Make sure your API server is running on http://localhost:3000")
        print("  4. 🧪 Test with trial creation or license validation")
        print("")

        print("🧪 Manual Testing:")
        print("  • Test API: curl http://localhost:3000/api/payments/pricing")
        print("  • Check server logs for incoming requests")
        print("  • Verify database for saved data")
        print("")

        print("⚠️  Important Notes:")
        print("  • LicenseAPIService initializes network manager at startup")
        print("  • Environment changes require app restart to take effect")
        print("  • Check Xcode console for 'Using LIVE API server!' message")
        print("")
    }

    /// Test trial license creation with current configuration
    func testTrialCreation() async {
        print("=== Testing Trial License Creation ===")

        do {
            let testEmail = "test-\(Int(Date().timeIntervalSince1970))@example.com"
            print("Creating trial license for: \(testEmail)")

            let response = try await LicenseAPIService.shared.createLicense(
                email: testEmail,
                licenseType: "trial"
            )

            print("✅ Trial license created successfully!")
            print("  License Key: \(response.licenseKey ?? "nil")")
            print("  Email: \(response.email ?? "nil")")
            print("  Type: \(response.licenseType ?? "nil")")
            print("  🚀 Data saved to your database!")

        } catch {
            print("❌ Trial creation failed: \(error.localizedDescription)")
            if let apiError = error as? LicenseAPIError {
                print("  Error Type: \(apiError)")
                print("  HTTP Status: \(apiError.httpStatusCode)")
            }
        }
        print("")
    }
}

// MARK: - Usage Instructions

/*
 To use this test:

 1. In your app delegate or a development view controller:

 let connectionTest = LiveAPIConnectionTest()
 Task {
     await connectionTest.runConnectionTest()
 }

 2. To test trial creation:

 Task {
     await connectionTest.testTrialCreation()
 }

 3. Check the Xcode console for detailed output

 */
