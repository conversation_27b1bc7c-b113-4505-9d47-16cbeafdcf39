import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @EnvironmentObject private var appDelegate: AppDelegate
    @State private var licenseKeyInput: String = ""
    @State private var emailInput: String = ""
    @State private var showingClearConfirmation = false
    @State private var pricingInfo: LicenseAPIService.PricingResponse?
    @State private var isLoadingPricing = false

    // MARK: - Helper Methods

    private func loadPricingInformation() {
        isLoadingPricing = true

        Task {
            do {
                let pricing = try await LicenseAPIService.shared.getPricing()
                await MainActor.run {
                    self.pricingInfo = pricing
                    self.isLoadingPricing = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingPricing = false
                    // Could add error handling here if needed
                }
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image(systemName: statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // Trial Information (if on active trial)
                            if licenseManager.licenseStatus == .trial
                                && licenseManager.isTrialActive
                            {
                                Divider()

                                HStack {
                                    Image(systemName: "clock")
                                        .foregroundColor(.orange)
                                        .frame(width: 16)

                                    Text("Trial Days Remaining")

                                    Spacer()

                                    Text("\(licenseManager.remainingTrialDays) days")
                                        .foregroundColor(.orange)
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .medium))
                                }
                                .snapbackRowStyle()
                            }

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // License Type
                                    HStack {
                                        Image(systemName: "doc.text")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("License Type")

                                        Spacer()

                                        Text(info.licenseType)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Registered User
                                    HStack {
                                        Image(systemName: "person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Expiration Date
                                    HStack {
                                        Image(systemName: "calendar")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Expires")

                                        Spacer()

                                        Text(info.expirationDisplayText)
                                            .foregroundColor(info.isExpired ? .red : .secondary)
                                    }
                                    .snapbackRowStyle()

                                    // Device Registration Status (if available)
                                    if let deviceToken = UserDefaults.standard.string(
                                        forKey: "SnapbackDeviceToken"),
                                        !deviceToken.isEmpty
                                    {
                                        Divider()

                                        HStack {
                                            Image(systemName: "desktopcomputer")
                                                .foregroundColor(.secondary)
                                                .frame(width: 16)

                                            Text("Device Registration")

                                            Spacer()

                                            Text("Registered")
                                                .foregroundColor(.green)
                                        }
                                        .snapbackRowStyle()
                                    }
                                }
                            }
                        }
                    }

                    // License Information Section
                    Text("License Information")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Email Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Email Address")

                                    Spacer()

                                    if licenseManager.isValidating {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    } else {
                                        TextField("<EMAIL>", text: $emailInput)
                                            .textFieldStyle(.roundedBorder)
                                            .disabled(
                                                licenseManager.isValidating
                                                    || (licenseManager.licenseStatus == .valid
                                                        && !licenseManager.licenseKey.isEmpty)
                                                    || (licenseManager.licenseStatus == .trial
                                                        && licenseManager.isTrialActive)
                                            )
                                            .disableAutocorrection(true)
                                            .textContentType(.emailAddress)
                                            .frame(width: 200)
                                    }
                                }

                                if (licenseManager.licenseStatus == .valid
                                    && !licenseManager.licenseKey.isEmpty)
                                    || (licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    Text(
                                        licenseManager.licenseStatus == .trial
                                            ? "Email and license key are locked during active trial period."
                                            : "Email and license key are locked when a valid license is active."
                                    )
                                    .snapbackCaptionStyle()
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // License Key Input Row - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("License Key")

                                    Spacer()

                                    TextField(
                                        "XXXX-XXXX-XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput
                                    )
                                    .textFieldStyle(.roundedBorder)
                                    .font(.system(.body, design: .monospaced))
                                    .disabled(
                                        licenseManager.isValidating
                                            || (licenseManager.licenseStatus == .valid
                                                && !licenseManager.licenseKey.isEmpty)
                                            || (licenseManager.licenseStatus == .trial
                                                && licenseManager.isTrialActive)
                                    )
                                    .disableAutocorrection(true)
                                    .autocorrectionDisabled(true)
                                    .textContentType(.none)
                                    .frame(width: 200)
                                    .onSubmit {
                                        Task {
                                            await licenseManager.setLicenseKeyAndEmail(
                                                licenseKeyInput, email: emailInput)
                                        }
                                    }
                                    .onChange(of: licenseKeyInput) { oldValue, newValue in
                                        // Auto-format license key with dashes, but preserve trial key format
                                        if newValue.hasPrefix("TRIAL-") {
                                            // Don't reformat trial keys - they have their own format
                                            return
                                        }

                                        let cleaned = newValue.replacingOccurrences(
                                            of: "-", with: ""
                                        ).uppercased()

                                        // Use the correct license key length from LicenseKeyFormatter
                                        if cleaned.count <= LicenseKeyFormatter.keyLength {
                                            let formatted = formatLicenseKey(cleaned)
                                            if formatted != newValue {
                                                licenseKeyInput = formatted
                                            }
                                        } else {
                                            // Truncate to maximum allowed length instead of reverting
                                            let truncated = String(
                                                cleaned.prefix(LicenseKeyFormatter.keyLength))
                                            licenseKeyInput = formatLicenseKey(truncated)
                                        }
                                    }
                                }
                            }
                            .snapbackRowStyle()

                            Divider()

                            // Action Buttons Section - following General tab pattern
                            VStack(alignment: .leading, spacing: 4) {
                                // Error Message
                                if let error = licenseManager.lastError {
                                    Text(error)
                                        .snapbackCaptionStyle()
                                        .foregroundColor(.red)
                                        .padding(.bottom, 8)
                                }

                                // Action Buttons - show based on license status
                                if licenseManager.licenseStatus == .valid
                                    || (licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    // Show unregister button for valid licenses and active trials
                                    HStack {
                                        Button("Unregister") {
                                            showingClearConfirmation = true
                                        }
                                        .buttonStyle(.bordered)
                                        .foregroundColor(.red)

                                        Spacer()
                                    }
                                } else if licenseManager.licenseStatus != .valid
                                    && !(licenseManager.licenseStatus == .trial
                                        && licenseManager.isTrialActive)
                                {
                                    // Show validate and unregister buttons for non-licensed states
                                    HStack(spacing: 12) {
                                        // Only show Validate button when not already licensed or on trial
                                        Button("Validate License") {
                                            Task {
                                                await licenseManager.setLicenseKeyAndEmail(
                                                    licenseKeyInput, email: emailInput)
                                            }
                                        }
                                        .buttonStyle(.bordered)
                                        .disabled(
                                            licenseKeyInput.isEmpty || emailInput.isEmpty
                                                || licenseManager.isValidating)

                                        // Unregister Button (only show if there's a license key)
                                        if !licenseManager.licenseKey.isEmpty {
                                            Button("Unregister") {
                                                showingClearConfirmation = true
                                            }
                                            .buttonStyle(.bordered)
                                            .foregroundColor(.red)
                                        }

                                        Spacer()
                                    }
                                }
                            }
                            .snapbackRowStyle()
                        }
                    }

                    // License Options Section
                    Text("License Options")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            if isLoadingPricing {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Loading pricing information...")
                                        .snapbackCaptionStyle()
                                }
                                .snapbackRowStyle()
                            } else if let pricing = pricingInfo {
                                // Standard License
                                VStack(alignment: .leading, spacing: 4) {
                                    HStack {
                                        Text("Standard License")
                                            .font(
                                                .system(
                                                    size: SnapbackTheme.FontSize.body,
                                                    weight: .medium))

                                        Spacer()

                                        Text(
                                            "$\(String(format: "%.2f", Double(pricing.standard.price) / 100))"
                                        )
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .semibold
                                            )
                                        )
                                        .foregroundColor(.primary)
                                    }

                                    Text(
                                        "\(pricing.standard.maxDevices) devices • \(pricing.standard.duration)"
                                    )
                                    .snapbackCaptionStyle()
                                }
                                .snapbackRowStyle()

                                Divider()

                                // Extended License
                                VStack(alignment: .leading, spacing: 4) {
                                    HStack {
                                        Text("Extended License")
                                            .font(
                                                .system(
                                                    size: SnapbackTheme.FontSize.body,
                                                    weight: .medium))

                                        Spacer()

                                        Text(
                                            "$\(String(format: "%.2f", Double(pricing.extended.price) / 100))"
                                        )
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .semibold
                                            )
                                        )
                                        .foregroundColor(.primary)
                                    }

                                    Text(
                                        "\(pricing.extended.maxDevices) devices • \(pricing.extended.duration)"
                                    )
                                    .snapbackCaptionStyle()
                                }
                                .snapbackRowStyle()
                            } else {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("License Pricing")
                                        .font(
                                            .system(
                                                size: SnapbackTheme.FontSize.body, weight: .medium))

                                    Text(
                                        "Visit our website to view current pricing and purchase options."
                                    )
                                    .snapbackCaptionStyle()

                                    Button("View Pricing") {
                                        loadPricingInformation()
                                    }
                                    .buttonStyle(.bordered)
                                    .padding(.top, 4)
                                }
                                .snapbackRowStyle()
                            }
                        }
                    }

                    Spacer(minLength: 20)
                }
                .padding()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Load current license key and email into input fields
            licenseKeyInput = licenseManager.licenseKey
            emailInput = licenseManager.userEmail

            // Debug logging for UI state
            print(
                "🔍 LICENSE UI DEBUG: onAppear - licenseStatus: \(licenseManager.licenseStatus), isTrialActive: \(licenseManager.isTrialActive), licenseKey: '\(licenseManager.licenseKey)', isValidating: \(licenseManager.isValidating)"
            )

            // Load pricing information
            if pricingInfo == nil && !isLoadingPricing {
                loadPricingInformation()
            }
        }
        .onChange(of: licenseManager.licenseKey) { _, newKey in
            // Update input field when license key changes (e.g., trial activation)
            licenseKeyInput = newKey
        }
        .onChange(of: licenseManager.userEmail) { _, newEmail in
            // Update input field when email changes (e.g., trial activation)
            emailInput = newEmail
        }
        .onChange(of: licenseManager.licenseStatus) { _, newStatus in
            // Debug logging for license status changes
            print("🔍 LICENSE UI DEBUG: licenseStatus changed to: \(newStatus), isTrialActive: \(licenseManager.isTrialActive), licenseKey: '\(licenseManager.licenseKey)'")
        }
        .onChange(of: licenseManager.isTrialActive) { _, newIsTrialActive in
            // Debug logging for trial status changes
            print("🔍 LICENSE UI DEBUG: isTrialActive changed to: \(newIsTrialActive), licenseStatus: \(licenseManager.licenseStatus)")
        }
        .alert("Unregister License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Unregister", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
                emailInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to unregister this license? This will remove the license from this device. You can re-register it later using the same license key."
            )
        }
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .valid:
            return "checkmark.circle.fill"
        case .trial:
            return "clock.fill"
        case .expired:
            return "exclamationmark.triangle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .unlicensed:
            return "questionmark.circle.fill"
        }
    }

    /// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
    /// Preserves trial key format (TRIAL-timestamp-suffix)
    private func formatLicenseKey(_ key: String) -> String {
        // Don't reformat trial keys - they have their own format
        if key.hasPrefix("TRIAL-") {
            return key
        }

        // Remove all non-alphanumeric characters
        let cleaned = key.replacingOccurrences(
            of: "[^A-Za-z0-9]", with: "", options: .regularExpression)

        // Add dashes every 4 characters
        var formatted = ""
        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }

}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
