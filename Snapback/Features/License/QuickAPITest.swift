import Foundation

/// Quick and simple API test - add this to your app delegate or a test view
class QuickAPITest {

    static func testConnection() async {
        print("🔍 Testing API Connection...")

        let apiService = LicenseAPIService.shared
        let config = LicenseAPIConfiguration.shared

        print("Environment: \(config.currentEnvironment.displayName)")
        print("Base URL: \(config.baseURL)")

        do {
            let pricing = try await apiService.getPricing()
            print("✅ API Connection Successful!")
            print("Trial Price: $\(pricing.trial.price/100)")
            print("🚀 Using REAL API server!")
        } catch {
            print("❌ API Connection Failed: \(error)")
        }
    }

    static func testTrialCreation() async {
        print("🧪 Testing Trial Creation...")

        let apiService = LicenseAPIService.shared
        let config = LicenseAPIConfiguration.shared
        let testEmail = "test-\(Int(Date().timeIntervalSince1970))@example.com"

        do {
            let response = try await apiService.createLicense(
                email: testEmail,
                licenseType: "trial"
            )

            print("✅ Trial Created!")
            print("License Key: \(response.licenseKey)")
            print("Email: \(response.email)")
            print("🚀 Data saved to your database!")

        } catch {
            print("❌ Trial Creation Failed: \(error)")
        }
    }
}

/*
Usage - Add this to your AppDelegate or a test view:

Task {
    await QuickAPITest.testConnection()
    await QuickAPITest.testTrialCreation()
}
*/
