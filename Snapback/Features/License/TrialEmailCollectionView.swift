import SwiftUI

/// Modal view for collecting user email when requesting a free trial
struct TrialEmailCollectionView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var emailInput: String = ""
    @State private var isRequestingTrial: Bool = false
    @State private var showingSuccess: Bool = false
    @State private var errorMessage: String?

    let onDismiss: () -> Void
    let onSuccess: () -> Void

    var body: some View {
        ScrollView {
            VStack(spacing: SnapbackTheme.Padding.large) {
                // Header - Native alert style
                VStack(spacing: 8) {
                    Image(systemName: "gift.fill")
                        .font(.system(size: 32))
                        .foregroundColor(.accentColor)

                    Text("Start Your Free Trial")
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("Get 15 days of full access to all Snapback features")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // Email Input Section - Following License Settings pattern
                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                            HStack {
                                Text("Email Address")
                                    .font(
                                        .system(
                                            size: SnapbackTheme.FontSize.body,
                                            weight: SnapbackTheme.FontWeight.body)
                                    )
                                    .foregroundColor(SnapbackTheme.Text.primary)

                                Spacer()

                                TextField("<EMAIL>", text: $emailInput)
                                    .textFieldStyle(.roundedBorder)
                                    .disabled(isRequestingTrial)
                                    .disableAutocorrection(true)
                                    .textContentType(.emailAddress)
                                    .frame(width: 200)
                                    .onSubmit {
                                        if isValidInput {
                                            requestTrial()
                                        }
                                    }
                            }

                            Text("We'll use this email to send you your trial license key")
                                .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                }

                // Error Message - Full width styling to match GroupBox behavior
                if let errorMessage = errorMessage {
                    HStack(alignment: .top, spacing: SnapbackTheme.Padding.small) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(SnapbackTheme.Text.error)
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .padding(.top, 2)  // Align with first line of text

                        Text(errorMessage)
                            .snapbackErrorStyle()
                            .fixedSize(horizontal: false, vertical: true)  // Allow vertical expansion
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: .infinity, alignment: .leading)  // Allow text to expand
                    }
                    .padding(SnapbackTheme.Padding.standard)  // Internal padding for content
                    .frame(maxWidth: .infinity)  // Force full width expansion
                    .background(SnapbackTheme.Text.error.opacity(0.1))
                    .cornerRadius(SnapbackTheme.CornerRadius.card)
                }

                // Success Message - Consistent styling
                if showingSuccess {
                    HStack(spacing: SnapbackTheme.Padding.small) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.system(size: SnapbackTheme.FontSize.caption))

                        Text("Trial activated successfully!")
                            .font(.system(size: SnapbackTheme.FontSize.body))
                            .foregroundColor(.green)
                    }
                    .padding(SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(SnapbackTheme.CornerRadius.card)
                }

                // Action Buttons - Consistent spacing
                HStack(spacing: SnapbackTheme.Padding.horizontal) {
                    // Cancel Button
                    Button("Cancel") {
                        print("🔍 TRIAL MODAL DEBUG: Cancel button tapped")
                        onDismiss()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                    .disabled(isRequestingTrial)

                    // Start Trial Button
                    Button(action: requestTrial) {
                        HStack(spacing: SnapbackTheme.Padding.small) {
                            if isRequestingTrial {
                                ProgressView()
                                    .scaleEffect(0.2)  // Reduced size to be proportional
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            }
                            Text(isRequestingTrial ? "Requesting..." : "Start Trial")
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .disabled(!isValidInput || isRequestingTrial)
                }

                // Terms and Privacy - Ensure full visibility
                VStack(spacing: SnapbackTheme.Padding.small) {
                    Text(
                        "By starting your trial, you agree to our Terms of Service and Privacy Policy"
                    )
                    .font(.caption2)
                    .foregroundColor(SnapbackTheme.Text.secondary)
                    .multilineTextAlignment(.center)
                    .fixedSize(horizontal: false, vertical: true)  // Allow vertical expansion
                    .lineLimit(nil)  // Remove line limit to prevent truncation
                    .frame(maxWidth: .infinity)  // Use full available width
                }
            }
            .padding(SnapbackTheme.Padding.large + SnapbackTheme.Padding.small)  // 20pt total
        }
        .frame(width: 400)
        .frame(maxHeight: 500)  // Maximum height before scrolling
        .background(SnapbackTheme.Background.window)
        .cornerRadius(SnapbackTheme.CornerRadius.card)
        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 2)
        .onChange(of: licenseManager.licenseStatus) { _, status in
            print("🔍 TRIAL MODAL DEBUG: License status changed to: \(status)")
            if status == .trial && isRequestingTrial {
                print("🔍 TRIAL MODAL DEBUG: Trial activated, calling showSuccess()")
                showSuccess()
            }
        }
        .onChange(of: licenseManager.lastError) { _, error in
            if let error = error, isRequestingTrial {
                // Display the actual server error message for better user feedback
                self.errorMessage = error
                self.isRequestingTrial = false
            }
        }
    }

    // MARK: - Computed Properties

    private var isValidInput: Bool {
        return !emailInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            && isValidEmail(emailInput.trimmingCharacters(in: .whitespacesAndNewlines))
    }

    // MARK: - Methods

    private func requestTrial() {
        let trimmedEmail = emailInput.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        guard isValidEmail(trimmedEmail) else {
            errorMessage = "Please enter a valid email address"
            return
        }

        // Clear any previous error
        errorMessage = nil
        isRequestingTrial = true

        Task {
            await licenseManager.requestTrialLicense(email: trimmedEmail)

            await MainActor.run {
                isRequestingTrial = false

                // Check for errors - success will be handled by onChange modifier
                // Display actual server error message for better user feedback
                if let error = licenseManager.lastError, licenseManager.licenseStatus != .trial {
                    errorMessage = error
                }
            }
        }
    }

    private func showSuccess() {
        print("🔍 TRIAL MODAL DEBUG: showSuccess() called, showingSuccess=\(showingSuccess)")

        // Prevent multiple success calls
        guard !showingSuccess else {
            print("🔍 TRIAL MODAL DEBUG: showSuccess() already called, ignoring duplicate")
            return
        }

        showingSuccess = true

        // Auto-dismiss after showing success for a bit longer to let user see the message
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            print("🔍 TRIAL MODAL DEBUG: Auto-closing modal after success, calling onSuccess()")
            self.onSuccess()
        }
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - Preview

#if DEBUG
    struct TrialEmailCollectionView_Previews: PreviewProvider {
        static var previews: some View {
            TrialEmailCollectionView(
                onDismiss: { print("Dismissed") },
                onSuccess: { print("Success") }
            )
            .preferredColorScheme(.light)

            TrialEmailCollectionView(
                onDismiss: { print("Dismissed") },
                onSuccess: { print("Success") }
            )
            .preferredColorScheme(.dark)
        }
    }

#endif
