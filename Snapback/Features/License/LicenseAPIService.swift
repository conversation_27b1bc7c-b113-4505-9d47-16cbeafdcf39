import CryptoKit
import Foundation

/// API service for license operations including trial requests and license validation
/// Implements the Snapback License Management API specification with live API integration
class LicenseAPIService {
    static let shared = LicenseAPIService()

    private let configuration = LicenseAPIConfiguration.shared
    private let networkManager: LicenseAPINetworkManager

    /// Logger for API-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseAPIService"

    private init() {
        // Always use real network manager - mock API removed for production
        self.networkManager = LicenseAPINetworkManager.shared
        logger.info(
            "🚀 REAL_API: LicenseAPIService initialized with production network manager",
            service: serviceName)
    }

    // MARK: - Configuration Properties

    /// Current API base URL (delegates to configuration)
    var baseURL: String {
        return configuration.baseURL
    }

    /// Current environment
    var currentEnvironment: LicenseAPIConfiguration.Environment {
        return configuration.currentEnvironment
    }

    // MARK: - Environment Management

    /// Override the current environment (useful for testing)
    func setEnvironmentOverride(_ environment: LicenseAPIConfiguration.Environment?) {
        configuration.setEnvironmentOverride(environment)
        // Note: This requires app restart to take effect due to network manager initialization
    }

    /// Get current configuration summary for debugging
    func getConfigurationSummary() -> [String: Any] {
        return configuration.getConfigurationSummary()
    }

    // MARK: - API Models

    // MARK: License Request Models
    struct LicenseValidationRequest: Codable {
        let licenseKey: String
        let deviceId: String
        let appVersion: String
    }

    struct LicenseCreationRequest: Codable {
        let email: String
        let licenseType: String
        let deviceId: String?
    }

    struct ResendLicenseRequest: Codable {
        let email: String
    }

    // MARK: Legacy Models (for backward compatibility)
    /// Request model for trial license request
    struct TrialRequest: Codable {
        let email: String
        let deviceId: String
        let appVersion: String
    }

    /// Response model for trial license request
    struct TrialResponse: Codable {
        let success: Bool
        let licenseKey: String?
        let expirationDate: String?  // ISO 8601 format
        let message: String?
        let error: String?
    }

    /// Request model for license validation (legacy)
    struct ValidationRequest: Codable {
        let licenseKey: String
        let email: String
        let deviceId: String
    }

    /// Response model for license validation (legacy)
    struct ValidationResponse: Codable {
        let valid: Bool
        let licenseType: String?
        let registeredUser: String?
        let email: String?
        let expirationDate: String?  // ISO 8601 format
        let features: [String]?
        let message: String?
        let error: String?
    }

    struct PricingResponse: Codable {
        let trial: PricingTier
        let standard: PricingTier
        let extended: PricingTier
        let additionalDevice: AdditionalDevicePricing
    }

    struct PricingTier: Codable {
        let price: Int
        let maxDevices: Int
        let duration: String
    }

    struct AdditionalDevicePricing: Codable {
        let price: Int
        let description: String
    }

    // MARK: License Response Models

    /// New nested license object structure
    struct LicenseObject: Codable {
        let id: String
        let licenseKey: String
        let email: String
        let licenseType: String
        let maxDevices: Int
        let expiresAt: Date?
        let createdAt: Date
        let updatedAt: Date
        let stripePaymentIntentId: String?
        let devicesUsed: Int
        let devices: [DeviceInfo]
    }

    /// Trial information structure
    struct TrialInfo: Codable {
        let emailSent: Bool
        let immediateAccess: Bool
        let note: String
    }

    /// Updated license validation response with nested structure and backward compatibility
    struct LicenseValidationResponse: Codable {
        let valid: Bool
        let message: String?

        // New nested structure
        let license: LicenseObject?

        // Legacy fields for backward compatibility
        let deviceToken: String?
        let licenseKey: String?
        let licenseType: String?
        let expiresAt: Date?
        let devicesUsed: Int?
        let maxDevices: Int?
        let trialInfo: TrialInfo?
        let trialDaysRemaining: Int?  // NEW: Server-calculated remaining trial days

        // Computed properties for easy access with backward compatibility
        var effectiveLicenseKey: String? {
            return license?.licenseKey ?? licenseKey
        }

        var effectiveLicenseType: String? {
            return license?.licenseType ?? licenseType
        }

        var effectiveExpiresAt: Date? {
            return license?.expiresAt ?? expiresAt
        }

        var effectiveDevicesUsed: Int? {
            return license?.devicesUsed ?? devicesUsed
        }

        var effectiveMaxDevices: Int? {
            return license?.maxDevices ?? maxDevices
        }

        var effectiveEmail: String? {
            return license?.email
        }

        var effectiveLicenseId: String? {
            return license?.id
        }

        var effectiveCreatedAt: Date? {
            return license?.createdAt
        }

        var effectiveUpdatedAt: Date? {
            return license?.updatedAt
        }

        var effectiveTrialDaysRemaining: Int? {
            return trialDaysRemaining
        }
    }

    struct LicenseCreationResponse: Codable {
        let message: String?

        // New nested structure
        let license: LicenseObject?

        // Legacy fields for backward compatibility
        let licenseKey: String?
        let licenseType: String?
        let email: String?
        let maxDevices: Int?
        let expiresAt: Date?
        let createdAt: Date?
        let trialInfo: TrialInfo?
        let trialDaysRemaining: Int?  // NEW: Server-calculated remaining trial days

        // Computed properties for easy access with backward compatibility
        var effectiveLicenseKey: String? {
            return license?.licenseKey ?? licenseKey
        }

        var effectiveLicenseType: String? {
            return license?.licenseType ?? licenseType
        }

        var effectiveEmail: String? {
            return license?.email ?? email
        }

        var effectiveMaxDevices: Int? {
            return license?.maxDevices ?? maxDevices
        }

        var effectiveExpiresAt: Date? {
            return license?.expiresAt ?? expiresAt
        }

        var effectiveCreatedAt: Date? {
            return license?.createdAt ?? createdAt
        }

        var effectiveLicenseId: String? {
            return license?.id
        }

        var effectiveUpdatedAt: Date? {
            return license?.updatedAt
        }

        var effectiveTrialDaysRemaining: Int? {
            return trialDaysRemaining
        }
    }

    struct LicenseStatusResponse: Codable {
        let licenseKey: String
        let licenseType: String
        let email: String
        let createdAt: Date
        let expiresAt: Date?
        let maxDevices: Int
        let devicesUsed: Int
        let isExpired: Bool
        let isActive: Bool
        let devices: [DeviceInfo]
        let trialDaysRemaining: Int?  // NEW: Server-calculated remaining trial days
    }

    struct DeviceInfo: Codable {
        let id: String
        let firstSeen: Date
        let lastSeen: Date
        let appVersion: String?  // Made optional to match new API structure
        let isActive: Bool
    }

    struct LicenseInfo: Codable {
        let licenseKey: String
        let licenseType: String
        let maxDevices: Int
        let expiresAt: Date?
        let email: String
    }

    struct ErrorResponse: Codable {
        let error: String
        let code: String?
        let details: String?
        let retryAfter: Int?
    }

    // MARK: - API Methods

    // MARK: Payment Endpoints

    /// Get pricing information for all license types
    func getPricing() async throws -> PricingResponse {
        // Always use real API - mock API disabled for production use
        return try await networkManager.get(endpoint: "/payments/pricing")
    }

    // MARK: - Date Decoding Helper

    /// Custom date decoding strategy that handles both ISO8601 with and without fractional seconds
    private func createFlexibleDateDecodingStrategy() -> JSONDecoder.DateDecodingStrategy {
        return .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // Try ISO8601 with fractional seconds first
            let iso8601WithFractionalSeconds = ISO8601DateFormatter()
            iso8601WithFractionalSeconds.formatOptions = [
                .withInternetDateTime, .withFractionalSeconds,
            ]
            if let date = iso8601WithFractionalSeconds.date(from: dateString) {
                self.logger.debug(
                    "DATE_PARSING: Successfully parsed date with fractional seconds: \(dateString) -> \(date)",
                    service: self.serviceName)
                return date
            }

            // Fallback to standard ISO8601 without fractional seconds
            let iso8601Standard = ISO8601DateFormatter()
            iso8601Standard.formatOptions = [.withInternetDateTime]
            if let date = iso8601Standard.date(from: dateString) {
                self.logger.debug(
                    "DATE_PARSING: Successfully parsed date without fractional seconds: \(dateString) -> \(date)",
                    service: self.serviceName)
                return date
            }

            self.logger.error(
                "DATE_PARSING: Failed to parse date string: \(dateString)",
                service: self.serviceName)
            throw DecodingError.dataCorruptedError(
                in: container, debugDescription: "Invalid date format: \(dateString)")
        }
    }

    // MARK: License Endpoints

    /// Create a new license (trial creation only - paid licenses are handled externally)
    func createLicense(
        email: String, licenseType: String, deviceId: String? = nil
    ) async throws -> LicenseCreationResponse {
        logger.info(
            "🚀 REAL_API: createLicense called - email: \(email), type: \(licenseType)",
            service: serviceName)

        let request = LicenseCreationRequest(
            email: email,
            licenseType: licenseType,
            deviceId: deviceId ?? getDeviceIdentifier()
        )

        // Always use real API - mock API disabled for production use
        return try await performCreateLicense(request)
    }

    /// Validate license and register device (new API format)
    func validateLicenseAndRegisterDevice(
        licenseKey: String, deviceId: String? = nil, appVersion: String? = nil
    ) async throws -> LicenseValidationResponse {
        logger.info(
            "🚀 REAL_API: validateLicenseAndRegisterDevice called - key: \(licenseKey.prefix(8))...",
            service: serviceName)

        let request = LicenseValidationRequest(
            licenseKey: licenseKey,
            deviceId: deviceId ?? getDeviceIdentifier(),
            appVersion: appVersion ?? getAppVersion()
        )

        // Always use real API - mock API disabled for production use
        let response: LicenseValidationResponse = try await networkManager.post(
            endpoint: "/licenses/validate", body: request)

        // Store device token if provided
        if let deviceToken = response.deviceToken {
            configuration.storeDeviceToken(deviceToken)
            logger.info(
                "LICENSE_API: Device token stored from network manager response",
                service: serviceName)
        }

        return response
    }

    /// Get license status
    func getLicenseStatus(licenseKey: String) async throws -> LicenseStatusResponse {
        logger.info(
            "🚀 REAL_API: getLicenseStatus called - key: \(licenseKey.prefix(8))...",
            service: serviceName)

        // Always use real API - mock API disabled for production use
        return try await performGetLicenseStatus(licenseKey)
    }

    /// Resend license email
    func resendLicenseEmail(email: String) async throws -> Bool {
        let request = ResendLicenseRequest(email: email)

        // Always use real API - mock API disabled for production use
        return try await performResendLicenseEmail(request)
    }

    // MARK: Legacy Methods (for backward compatibility)

    /// Request a trial license for the given email
    func requestTrialLicense(email: String) async throws -> TrialResponse {
        let deviceId = getDeviceIdentifier()
        let appVersion = getAppVersion()

        let request = TrialRequest(
            email: email,
            deviceId: deviceId,
            appVersion: appVersion
        )

        // Always use real API - mock API disabled for production use
        return try await networkManager.post(endpoint: "/licenses/create", body: request)
    }

    /// Validate a license key with email (legacy)
    func validateLicense(licenseKey: String, email: String) async throws -> ValidationResponse {
        let deviceId = getDeviceIdentifier()

        let request = ValidationRequest(
            licenseKey: licenseKey,
            email: email,
            deviceId: deviceId
        )

        // Always use real API - mock API disabled for production use
        return try await performValidationRequest(request)
    }

    // MARK: - Production Implementation

    /// Perform actual trial request to production API
    private func performTrialRequest(_ request: TrialRequest) async throws -> TrialResponse {
        logger.info(
            "🚀 REAL_API: performTrialRequest starting - email: \(request.email), deviceId: \(request.deviceId)",
            service: serviceName)

        let url = URL(string: "\(baseURL)/api/trial/request")!
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            logger.error(
                "🚀 REAL_API: performTrialRequest failed - status: \((response as? HTTPURLResponse)?.statusCode ?? -1)",
                service: serviceName)
            throw APIError.networkError("Failed to request trial license")
        }

        // Log raw response for debugging
        if let responseString = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: Raw response received: \(responseString)", service: serviceName)
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
        let decodedResponse = try decoder.decode(TrialResponse.self, from: data)
        logger.info(
            "🚀 REAL_API: performTrialRequest success - licenseKey: \(decodedResponse.licenseKey?.prefix(8) ?? "nil")..., expirationDate: \(decodedResponse.expirationDate ?? "nil")",
            service: serviceName)

        return decodedResponse
    }

    /// Perform actual validation request to production API
    private func performValidationRequest(_ request: ValidationRequest) async throws
        -> ValidationResponse
    {
        let url = URL(string: "\(baseURL)/api/license/validate")!

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            throw APIError.networkError("Failed to validate license")
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
        return try decoder.decode(ValidationResponse.self, from: data)
    }

    // MARK: Production API Methods

    /// Perform actual create license request to production API
    private func performCreateLicense(_ request: LicenseCreationRequest) async throws
        -> LicenseCreationResponse
    {
        let url = URL(string: "\(baseURL)/licenses/create")!

        logger.info(
            "🚀 REAL_API: performCreateLicense starting - email: \(request.email), type: \(request.licenseType), deviceId: \(request.deviceId ?? "nil")",
            service: serviceName)
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            logger.error(
                "🚀 REAL_API: performCreateLicense failed to encode request: \(error)",
                service: serviceName)
            throw APIError.networkError("Failed to encode license creation request")
        }

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        // Log raw response for debugging
        if let rawResponse = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: performCreateLicense raw response: \(rawResponse)",
                service: serviceName)
        } else {
            logger.warning(
                "🚀 REAL_API: performCreateLicense unable to decode raw response as UTF-8 string",
                service: serviceName)
        }

        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error(
                "🚀 REAL_API: performCreateLicense invalid HTTP response type", service: serviceName)
            throw APIError.networkError("Invalid HTTP response")
        }

        logger.info(
            "🚀 REAL_API: performCreateLicense HTTP status code: \(httpResponse.statusCode)",
            service: serviceName)

        guard httpResponse.statusCode == 201 else {
            logger.warning(
                "🚀 REAL_API: performCreateLicense unexpected status code \(httpResponse.statusCode), expected 201",
                service: serviceName)
            throw APIError.networkError(
                "Failed to create license - HTTP \(httpResponse.statusCode)")
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            let parsedResponse = try decoder.decode(LicenseCreationResponse.self, from: data)

            logger.info(
                "🚀 REAL_API: performCreateLicense success - licenseKey: \(parsedResponse.effectiveLicenseKey?.prefix(8) ?? "nil")..., type: \(parsedResponse.effectiveLicenseType ?? "nil"), trialDaysRemaining: \(parsedResponse.effectiveTrialDaysRemaining?.description ?? "nil")",
                service: serviceName)

            return parsedResponse
        } catch {
            logger.error(
                "🚀 REAL_API: performCreateLicense failed to parse JSON response: \(error)",
                service: serviceName)
            throw APIError.networkError(
                "Failed to parse license creation response: \(error.localizedDescription)")
        }
    }

    /// Perform actual validate license and register device request to production API
    private func performValidateLicenseAndRegisterDevice(_ request: LicenseValidationRequest)
        async throws -> LicenseValidationResponse
    {
        let url = URL(string: "\(baseURL)/licenses/validate")!

        logger.info(
            "🚀 REAL_API: performValidateLicenseAndRegisterDevice starting - key: \(request.licenseKey.prefix(8))..., deviceId: \(request.deviceId)",
            service: serviceName)
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice failed to encode request: \(error)",
                service: serviceName)
            throw APIError.networkError("Failed to encode license validation request")
        }

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        // Log raw response for debugging
        if let rawResponse = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice raw response: \(rawResponse)",
                service: serviceName)
        } else {
            logger.warning(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice unable to decode raw response as UTF-8 string",
                service: serviceName)
        }

        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice invalid HTTP response type",
                service: serviceName)
            throw APIError.networkError("Invalid HTTP response")
        }

        logger.info(
            "🚀 REAL_API: performValidateLicenseAndRegisterDevice HTTP status code: \(httpResponse.statusCode)",
            service: serviceName)

        guard httpResponse.statusCode == 200 else {
            logger.warning(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice unexpected status code \(httpResponse.statusCode), expected 200",
                service: serviceName)
            throw APIError.networkError(
                "Failed to validate license - HTTP \(httpResponse.statusCode)")
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            let parsedResponse = try decoder.decode(LicenseValidationResponse.self, from: data)

            if parsedResponse.valid {
                logger.info(
                    "🚀 REAL_API: performValidateLicenseAndRegisterDevice success - key: \(request.licenseKey.prefix(8))..., type: \(parsedResponse.effectiveLicenseType ?? "nil"), devices: \(parsedResponse.effectiveDevicesUsed ?? 0)/\(parsedResponse.effectiveMaxDevices ?? 0), trialDaysRemaining: \(parsedResponse.effectiveTrialDaysRemaining?.description ?? "nil")",
                    service: serviceName)
            } else {
                logger.warning(
                    "🚀 REAL_API: performValidateLicenseAndRegisterDevice validation failed - key: \(request.licenseKey.prefix(8))..., message: \(parsedResponse.message ?? "no message")",
                    service: serviceName)
            }

            // Log trial info if present
            if let trialInfo = parsedResponse.trialInfo {
                logger.info(
                    "🚀 REAL_API: performValidateLicenseAndRegisterDevice trial info - emailSent: \(trialInfo.emailSent), immediateAccess: \(trialInfo.immediateAccess)",
                    service: serviceName)
            }

            return parsedResponse
        } catch {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice failed to parse JSON response: \(error)",
                service: serviceName)
            throw APIError.networkError(
                "Failed to parse license validation response: \(error.localizedDescription)")
        }
    }

    /// Perform actual get license status request to production API
    private func performGetLicenseStatus(_ licenseKey: String) async throws -> LicenseStatusResponse
    {
        let url = URL(string: "\(baseURL)/licenses/status/\(licenseKey)")!

        logger.info(
            "🚀 REAL_API: performGetLicenseStatus starting - key: \(licenseKey.prefix(8))...",
            service: serviceName)
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        // Log raw response for debugging
        if let rawResponse = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: performGetLicenseStatus raw response: \(rawResponse)",
                service: serviceName)
        } else {
            logger.warning(
                "🚀 REAL_API: performGetLicenseStatus unable to decode raw response as UTF-8 string",
                service: serviceName)
        }

        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error(
                "🚀 REAL_API: performGetLicenseStatus invalid HTTP response type",
                service: serviceName)
            throw APIError.networkError("Invalid HTTP response")
        }

        logger.info(
            "🚀 REAL_API: performGetLicenseStatus HTTP status code: \(httpResponse.statusCode)",
            service: serviceName)

        guard httpResponse.statusCode == 200 else {
            logger.warning(
                "🚀 REAL_API: performGetLicenseStatus unexpected status code \(httpResponse.statusCode), expected 200",
                service: serviceName)
            throw APIError.networkError(
                "Failed to get license status - HTTP \(httpResponse.statusCode)")
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            let parsedResponse = try decoder.decode(LicenseStatusResponse.self, from: data)

            logger.info(
                "🚀 REAL_API: performGetLicenseStatus success - key: \(licenseKey.prefix(8))..., type: \(parsedResponse.licenseType), email: \(parsedResponse.email), trialDaysRemaining: \(parsedResponse.trialDaysRemaining?.description ?? "nil")",
                service: serviceName)

            return parsedResponse
        } catch {
            logger.error(
                "🚀 REAL_API: performGetLicenseStatus failed to parse JSON response: \(error)",
                service: serviceName)
            throw APIError.networkError(
                "Failed to parse license status response: \(error.localizedDescription)")
        }
    }

    /// Perform actual resend license email request to production API
    private func performResendLicenseEmail(_ request: ResendLicenseRequest) async throws -> Bool {
        let url = URL(string: "\(baseURL)/licenses/resend")!

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (_, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            throw APIError.networkError("Failed to resend license email")
        }

        // If we get here, the request was successful
        return true
    }

    // MARK: - Helper Methods

    /// Get device identifier
    private func getDeviceIdentifier() -> String {
        return LicenseKeyFormatter.generateDeviceID()
    }

    /// Get app version
    private func getAppVersion() -> String {
        return LicenseKeyFormatter.getAppVersion()
    }

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        return LicenseKeyFormatter.isValidEmail(email)
    }
}

// MARK: - API Errors

/// API error types matching the Snapback License Management API specification
enum APIError: Error, LocalizedError {
    case validationError(String)
    case unauthorized
    case invalidToken
    case notFound(String)
    case licenseNotFound
    case deviceNotRegistered
    case licenseExpired
    case maxDevicesReached
    case rateLimitExceeded(retryAfter: Int)
    case internalError
    case networkError(String)
    case invalidResponse
    case invalidEmail
    case serverError(String)

    var errorDescription: String? {
        switch self {
        case .validationError(let message):
            return message
        case .unauthorized:
            return "Authentication failed. Please re-validate your license."
        case .invalidToken:
            return "Device token is invalid or expired"
        case .notFound(let resource):
            return "\(resource) not found"
        case .licenseNotFound:
            return "License key not found"
        case .deviceNotRegistered:
            return "Device not registered with license"
        case .licenseExpired:
            return "License has expired. Please renew your license."
        case .maxDevicesReached:
            return "Maximum number of devices reached for this license."
        case .rateLimitExceeded(let retryAfter):
            return "Too many requests. Please wait \(retryAfter) seconds."
        case .internalError:
            return "Server internal error. Please try again later."
        case .networkError(let message):
            return message
        case .invalidResponse:
            return "Invalid response from server"
        case .invalidEmail:
            return "Invalid email address"
        case .serverError(let message):
            return message
        }
    }

    /// HTTP status code associated with the error
    var httpStatusCode: Int {
        switch self {
        case .validationError:
            return 400
        case .unauthorized, .invalidToken:
            return 401
        case .notFound, .licenseNotFound, .deviceNotRegistered:
            return 404
        case .maxDevicesReached:
            return 409
        case .licenseExpired:
            return 410
        case .rateLimitExceeded:
            return 429
        case .internalError:
            return 500
        default:
            return 0  // Client-side errors
        }
    }
}
