import Foundation

/// Integration test helper for verifying live API connectivity
/// This is a development utility to test the API integration without running the full app
class LicenseAPIIntegrationTest {
    private let apiService = LicenseAPIService.shared
    private let configuration = LicenseAPIConfiguration.shared

    /// Test basic API connectivity and configuration
    func testAPIConfiguration() async {
        print("=== Snapback License API Integration Test ===")
        print("")

        // Print current configuration
        let config = apiService.getConfigurationSummary()
        print("Current Configuration:")
        for (key, value) in config.sorted(by: { $0.key < $1.key }) {
            print("  \(key): \(value)")
        }
        print("")

        // Test environment switching
        print("Testing Environment Switching:")
        print("  Current Environment: \(configuration.currentEnvironment.displayName)")
        print("  Base URL: \(configuration.baseURL)")
        print("")
    }

    /// Test pricing endpoint (safe to call without authentication)
    func testPricingEndpoint() async {
        print("Testing Pricing Endpoint...")

        do {
            let pricing = try await apiService.getPricing()
            print("✅ Pricing endpoint successful")
            print("  Trial: $\(pricing.trial.price/100) for \(pricing.trial.maxDevices) device(s)")
            print(
                "  Standard: $\(pricing.standard.price/100) for \(pricing.standard.maxDevices) device(s)"
            )
            print(
                "  Extended: $\(pricing.extended.price/100) for \(pricing.extended.maxDevices) device(s)"
            )
        } catch {
            print("❌ Pricing endpoint failed: \(error.localizedDescription)")
            if let apiError = error as? LicenseAPIError {
                print("  Error Type: \(apiError)")
                print("  HTTP Status: \(apiError.httpStatusCode)")
                print("  Is Retryable: \(apiError.isRetryable)")
            }
        }
        print("")
    }

    /// Test license key formatting utilities
    func testLicenseKeyFormatting() {
        print("Testing License Key Formatting:")

        let testKeys = [
            "ABCD-EFGH-IJKL-MNOP-QRST-UVWX",
            "abcdefghijklmnopqrstuvwx",
            "ABCD EFGH IJKL MNOP QRST UVWX",
            "invalid-key-with-0-and-1",
            "TRIAL-12345",
            "MOCK-TEST-KEY",
        ]

        for key in testKeys {
            let normalized = key.normalizedAsLicenseKey
            let formatted = key.formattedAsLicenseKey
            let validation = LicenseKeyFormatter.validateLicenseKeyFormat(key)

            print("  Input: '\(key)'")
            print("    Normalized: '\(normalized)'")
            print("    Formatted: '\(formatted)'")
            print("    Valid: \(validation.isValid)")
            if let error = validation.error {
                print("    Error: \(error)")
            }
            print("    Is Trial: \(key.isTrialLicenseKey)")
            print("    Is Mock: \(key.isMockLicenseKey)")
            print("")
        }
    }

    /// Test device ID generation
    func testDeviceIDGeneration() {
        print("Testing Device ID Generation:")

        let deviceId1 = LicenseKeyFormatter.generateDeviceID()
        let deviceId2 = LicenseKeyFormatter.generateDeviceID()

        print("  Device ID 1: \(deviceId1)")
        print("  Device ID 2: \(deviceId2)")
        print("  IDs Match (should be true): \(deviceId1 == deviceId2)")
        print("  ID Length: \(deviceId1.count)")
        print("")

        // Test clearing and regenerating
        LicenseKeyFormatter.clearStoredDeviceID()
        let deviceId3 = LicenseKeyFormatter.generateDeviceID()
        print("  After clearing:")
        print("  Device ID 3: \(deviceId3)")
        print("  ID3 != ID1 (should be true): \(deviceId3 != deviceId1)")
        print("")
    }

    /// Test error handling with invalid requests
    func testErrorHandling() async {
        print("Testing Error Handling:")

        // Test with invalid license key (should fail gracefully)
        do {
            _ = try await apiService.validateLicenseAndRegisterDevice(
                licenseKey: "INVALID-KEY",
                deviceId: "test-device"
            )
            print("❌ Expected validation to fail for invalid key")
        } catch {
            print("✅ Validation correctly failed for invalid key")
            if let apiError = error as? LicenseAPIError {
                print("  Error: \(apiError.localizedDescription)")
                print("  Recovery: \(apiError.recoverySuggestion ?? "None")")
            }
        }
        print("")
    }

    /// Test network manager configuration
    func testNetworkConfiguration() {
        print("Testing Network Configuration:")

        print("  Request Timeout: \(configuration.requestTimeout)s")
        print("  Max Retry Attempts: \(configuration.maxRetryAttempts)")
        print("  Base Retry Delay: \(configuration.baseRetryDelay)s")
        print("  User Agent: \(configuration.userAgent)")
        print("  Request Signing Enabled: \(configuration.isRequestSigningEnabled)")
        print("")

        // Test retry configuration
        let retryConfig = RetryConfiguration.default
        print("  Retry Configuration:")
        print("    Max Attempts: \(retryConfig.maxAttempts)")
        print("    Base Delay: \(retryConfig.baseDelay)s")
        print("    Max Delay: \(retryConfig.maxDelay)s")
        print("    Backoff Multiplier: \(retryConfig.backoffMultiplier)")
        print("")
    }

    /// Run all tests
    func runAllTests() async {
        await testAPIConfiguration()
        await testPricingEndpoint()
        testLicenseKeyFormatting()
        testDeviceIDGeneration()
        await testErrorHandling()
        testNetworkConfiguration()

        print("=== Integration Test Complete ===")
    }

    /// Test switching to live API (for development testing)
    func testLiveAPISwitch() async {
        print("=== Testing Live API Switch ===")

        // Switch to development environment
        configuration.setEnvironmentOverride(.development)
        print("Switched to development environment")
        print("Base URL: \(configuration.baseURL)")

        // Note: This requires app restart to take effect due to network manager initialization
        print("Note: Environment changes require app restart to take effect")
        print("")

        // Reset to default
        configuration.setEnvironmentOverride(nil)
        print("Reset to default environment")
        print("Base URL: \(configuration.baseURL)")
        print("")
    }
}

// MARK: - Usage Example

/*
 To use this integration test in development:

 1. In your app delegate or a development view controller:

 let integrationTest = LicenseAPIIntegrationTest()
 Task {
     await integrationTest.runAllTests()
 }

 2. To test live API connectivity (requires server to be running):

 Task {
     await integrationTest.testLiveAPISwitch()
 }

 3. To test specific functionality:

 Task {
     await integrationTest.testPricingEndpoint()
 }

 */
