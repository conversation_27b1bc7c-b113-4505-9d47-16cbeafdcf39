import CryptoKit
import Foundation

/// Utility class for license key formatting and device ID generation
/// Implements the Snapback License Management API specification for key handling
class LicenseKeyFormatter {

    // MARK: - License Key Format Constants

    /// Valid character set for license keys (excludes confusing characters: 0, O, 1, I, L)
    static let validCharacterSet = "ABCDEFGHJKMNPQRSTUVWXYZ23456789"

    /// Expected license key length (without dashes)
    static let keyLength = 24

    /// Number of characters per group when displaying with dashes
    static let groupSize = 4

    // MARK: - License Key Formatting

    /// Format a license key for display with dashes (XXXX-XXXX-XXXX-XXXX-XXXX-XXXX)
    /// - Parameter licenseKey: The license key to format (with or without dashes)
    /// - Returns: Formatted license key with dashes for display
    static func formatLicenseKeyForDisplay(_ licenseKey: String) -> String {
        let normalized = normalizeLicenseKeyForAPI(licenseKey)

        // Return empty string if the key is empty
        guard !normalized.isEmpty else { return "" }

        var formatted = ""
        for (index, character) in normalized.enumerated() {
            if index > 0 && index % groupSize == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }
        return formatted
    }

    /// Normalize a license key for API calls (remove dashes, uppercase)
    /// - Parameter licenseKey: The license key to normalize
    /// - Returns: Normalized license key without dashes, uppercase
    static func normalizeLicenseKeyForAPI(_ licenseKey: String) -> String {
        return
            licenseKey
            .replacingOccurrences(of: "-", with: "")
            .replacingOccurrences(of: " ", with: "")
            .uppercased()
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// Validate license key format
    /// - Parameter licenseKey: The license key to validate
    /// - Returns: Validation result with details
    static func validateLicenseKeyFormat(_ licenseKey: String) -> LicenseKeyValidation {
        let normalized = normalizeLicenseKeyForAPI(licenseKey)

        // Check if empty
        guard !normalized.isEmpty else {
            return LicenseKeyValidation(isValid: false, error: "License key cannot be empty")
        }

        // Check length
        guard normalized.count == keyLength else {
            return LicenseKeyValidation(
                isValid: false,
                error:
                    "License key must be exactly \(keyLength) characters long (found \(normalized.count))"
            )
        }

        // Check character set
        for char in normalized {
            if !validCharacterSet.contains(char) {
                return LicenseKeyValidation(
                    isValid: false,
                    error: "License key contains invalid character: \(char)"
                )
            }
        }

        return LicenseKeyValidation(isValid: true, error: nil)
    }

    /// Check if a license key appears to be a trial key (starts with TRIAL-)
    /// - Parameter licenseKey: The license key to check
    /// - Returns: True if it appears to be a trial key
    static func isTrialKey(_ licenseKey: String) -> Bool {
        return licenseKey.uppercased().hasPrefix("TRIAL-")
    }

    /// Check if a license key appears to be a mock/test key
    /// - Parameter licenseKey: The license key to check
    /// - Returns: True if it appears to be a mock key
    static func isMockKey(_ licenseKey: String) -> Bool {
        let normalized = normalizeLicenseKeyForAPI(licenseKey)
        let mockPrefixes = ["MOCK", "TEST", "DEMO", "SNAPBACK"]
        return mockPrefixes.contains { normalized.hasPrefix($0) }
    }

    // MARK: - Device ID Generation

    /// Generate a unique device identifier using system information
    /// - Returns: SHA256 hash of system-specific identifiers
    static func generateDeviceID() -> String {
        // Use persistent device identifier if available
        let key = "SnapbackDeviceIdentifier"
        if let existing = UserDefaults.standard.string(forKey: key) {
            return existing
        }

        // Generate new device ID based on system information
        let systemInfo = [
            ProcessInfo.processInfo.hostName,
            ProcessInfo.processInfo.operatingSystemVersionString,
            getHardwareUUID() ?? UUID().uuidString,
        ].joined(separator: "-")

        let data = Data(systemInfo.utf8)
        let hash = SHA256.hash(data: data)
        let deviceId = hash.compactMap { String(format: "%02x", $0) }.joined()

        // Store for future use
        UserDefaults.standard.set(deviceId, forKey: key)
        return deviceId
    }

    /// Get hardware UUID if available
    /// - Returns: Hardware UUID string or nil if not available
    private static func getHardwareUUID() -> String? {
        let platformExpert = IOServiceGetMatchingService(
            kIOMainPortDefault,
            IOServiceMatching("IOPlatformExpertDevice")
        )

        guard platformExpert > 0 else { return nil }

        defer { IOObjectRelease(platformExpert) }

        guard
            let serialNumberAsCFString = IORegistryEntryCreateCFProperty(
                platformExpert,
                kIOPlatformUUIDKey as CFString,
                kCFAllocatorDefault,
                0
            )?.takeUnretainedValue()
        else {
            return nil
        }

        return serialNumberAsCFString as? String
    }

    /// Clear stored device ID (useful for testing)
    static func clearStoredDeviceID() {
        UserDefaults.standard.removeObject(forKey: "SnapbackDeviceIdentifier")
    }

    // MARK: - App Version Utilities

    /// Get current app version string
    /// - Returns: App version in format "1.0.0"
    static func getAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }

    /// Get current app build number
    /// - Returns: App build number
    static func getBuildNumber() -> String {
        return Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }

    /// Get full app version string with build number
    /// - Returns: App version with build number in format "1.0.0 (123)"
    static func getFullAppVersion() -> String {
        let version = getAppVersion()
        let build = getBuildNumber()
        return "\(version) (\(build))"
    }

    // MARK: - Email Validation

    /// Validate email format
    /// - Parameter email: Email address to validate
    /// - Returns: True if email format is valid
    static func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - Supporting Types

/// License key validation result
struct LicenseKeyValidation {
    let isValid: Bool
    let error: String?

    /// Validation passed
    static let valid = LicenseKeyValidation(isValid: true, error: nil)

    /// Create validation failure
    static func invalid(_ error: String) -> LicenseKeyValidation {
        return LicenseKeyValidation(isValid: false, error: error)
    }
}

// MARK: - Extensions

extension String {
    /// Format this string as a license key for display
    var formattedAsLicenseKey: String {
        return LicenseKeyFormatter.formatLicenseKeyForDisplay(self)
    }

    /// Normalize this string as a license key for API calls
    var normalizedAsLicenseKey: String {
        return LicenseKeyFormatter.normalizeLicenseKeyForAPI(self)
    }

    /// Check if this string is a valid license key format
    var isValidLicenseKeyFormat: Bool {
        return LicenseKeyFormatter.validateLicenseKeyFormat(self).isValid
    }

    /// Check if this string appears to be a trial license key
    var isTrialLicenseKey: Bool {
        return LicenseKeyFormatter.isTrialKey(self)
    }

    /// Check if this string appears to be a mock license key
    var isMockLicenseKey: Bool {
        return LicenseKeyFormatter.isMockKey(self)
    }
}
