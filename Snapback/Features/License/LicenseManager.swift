import Foundation
import SwiftUI

/// Manages license validation and storage for the Snapback app
class LicenseManager: ObservableObject {
    static let shared = LicenseManager()

    // MARK: - Access Control Properties

    /// Computed property that determines if the user has full access
    /// When license system is disabled, always returns true
    /// When license system is enabled, checks actual license status
    var hasFullAccess: Bool {
        if FeatureFlags.isLicenseSystemDisabled {
            return true
        }
        return licenseStatus == .valid || licenseStatus == .trial
    }

    /// Computed property that indicates if the license system is active
    /// Used by UI components to determine whether to show license-related elements
    var isLicenseSystemEnabled: Bool {
        return !FeatureFlags.isLicenseSystemDisabled
    }

    /// Published properties for UI binding
    @Published var licenseStatus: LicenseStatus = .unlicensed
    @Published var licenseKey: String = ""
    @Published var userEmail: String = ""
    @Published var licenseInfo: LicenseInfo?
    @Published var isValidating: Bool = false
    @Published var lastError: String?

    /// Trial-related properties
    @Published var trialStartDate: Date? {
        didSet {
            logger.info(
                "🔍 TRIAL DATE DEBUG: trialStartDate updated to: \(trialStartDate?.description ?? "nil")",
                service: serviceName)
        }
    }
    @Published var trialEndDate: Date? {
        didSet {
            logger.info(
                "🔍 TRIAL DATE DEBUG: trialEndDate updated to: \(trialEndDate?.description ?? "nil")",
                service: serviceName)
        }
    }
    @Published var remainingTrialDays: Int = 0 {
        didSet {
            logger.info(
                "🔍 TRIAL DAYS DEBUG: remainingTrialDays updated to: \(remainingTrialDays)",
                service: serviceName)
        }
    }
    @Published var isTrialActive: Bool = false {
        didSet {
            logger.info(
                "🔍 TRIAL STATUS DEBUG: isTrialActive updated to: \(isTrialActive)",
                service: serviceName)
        }
    }

    /// Logger for license-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseManager"

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let licenseKey = "SnapbackLicenseKey"
        static let userEmail = "SnapbackUserEmail"
        static let licenseStatus = "SnapbackLicenseStatus"
        static let licenseInfo = "SnapbackLicenseInfo"
        static let lastValidation = "SnapbackLastLicenseValidation"

        // Trial-related keys
        static let trialStartDate = "SnapbackTrialStartDate"
        static let trialEndDate = "SnapbackTrialEndDate"
        static let firstLaunchDate = "SnapbackFirstLaunchDate"
        static let trialChecksum = "SnapbackTrialChecksum"
    }

    /// License validation endpoint (placeholder - replace with actual endpoint)
    private let validationEndpoint = "https://api.snapback.app/license/validate"

    /// Trial configuration
    private let trialDurationDays = 14

    /// Real-time computed property for remaining trial days (for debugging)
    var computedRemainingTrialDays: Int {
        guard trialStartDate != nil,
            let endDate = trialEndDate
        else {
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Missing trial dates - returning 0", service: serviceName)
            return 0
        }

        let now = Date()
        logger.info("🔍 COMPUTED DAYS DEBUG: Computing remaining days", service: serviceName)
        logger.info("🔍 COMPUTED DAYS DEBUG: Current time: \(now)", service: serviceName)
        logger.info("🔍 COMPUTED DAYS DEBUG: Trial end date: \(endDate)", service: serviceName)

        if now <= endDate {
            let calendar = Calendar.current

            // FIX: Use inclusive day calculation - count from start of today to start of end day + 1
            let startOfToday = calendar.startOfDay(for: now)
            let startOfEndDay = calendar.startOfDay(for: endDate)
            let daysBetween =
                calendar.dateComponents([.day], from: startOfToday, to: startOfEndDay).day ?? 0

            // Add 1 to make it inclusive (if trial ends today, show 1 day remaining)
            let inclusiveDays = daysBetween + 1

            // DETAILED ANALYSIS: Log calculation details for comparison
            let timeInterval = endDate.timeIntervalSince(now)
            let totalDays = timeInterval / (24 * 3600)
            let oldCalculatedDays = calendar.dateComponents([.day], from: now, to: endDate).day ?? 0

            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Days between start of days: \(daysBetween)",
                service: serviceName)
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Inclusive days (daysBetween + 1): \(inclusiveDays)",
                service: serviceName)
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Old calculation (raw): \(oldCalculatedDays)",
                service: serviceName)
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Total days (decimal): \(totalDays)", service: serviceName)

            let finalDays = max(0, inclusiveDays)
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Final computed days: \(finalDays)", service: serviceName)

            return finalDays
        } else {
            logger.info(
                "🔍 COMPUTED DAYS DEBUG: Trial has expired - returning 0", service: serviceName)
            return 0
        }
    }

    /// Calculate remaining trial days using server-provided dates
    /// This ensures consistent calculation across all scenarios
    private func calculateRemainingTrialDays(startDate: Date, endDate: Date) -> Int {
        let now = Date()
        logger.info(
            "🔍 SERVER DATE CALCULATION: Computing remaining days using server dates",
            service: serviceName)
        logger.info(
            "🔍 SERVER DATE CALCULATION: Server start date: \(startDate)", service: serviceName)
        logger.info("🔍 SERVER DATE CALCULATION: Server end date: \(endDate)", service: serviceName)
        logger.info("🔍 SERVER DATE CALCULATION: Current time: \(now)", service: serviceName)

        if now <= endDate {
            let calendar = Calendar.current

            // Use inclusive day calculation - count from start of today to start of end day + 1
            let startOfToday = calendar.startOfDay(for: now)
            let startOfEndDay = calendar.startOfDay(for: endDate)
            let daysBetween =
                calendar.dateComponents([.day], from: startOfToday, to: startOfEndDay).day ?? 0

            // Add 1 to make it inclusive (if trial ends today, show 1 day remaining)
            let inclusiveDays = daysBetween + 1
            let finalDays = max(0, inclusiveDays)

            logger.info(
                "🔍 SERVER DATE CALCULATION: Days between start of days: \(daysBetween)",
                service: serviceName)
            logger.info(
                "🔍 SERVER DATE CALCULATION: Inclusive days (daysBetween + 1): \(inclusiveDays)",
                service: serviceName)
            logger.info(
                "🔍 SERVER DATE CALCULATION: Final calculated days: \(finalDays)",
                service: serviceName)

            return finalDays
        } else {
            logger.info(
                "🔍 SERVER DATE CALCULATION: Trial has expired - returning 0", service: serviceName)
            return 0
        }
    }

    /// Debug method to compare stored vs computed remaining days
    /// Now shows server-provided vs client-calculated values for comparison
    func debugTrialDaysComparison() {
        let stored = remainingTrialDays
        let computed = computedRemainingTrialDays

        logger.info(
            "🔍 COMPARISON DEBUG: Stored remainingTrialDays (server-provided or fallback): \(stored)",
            service: serviceName)
        logger.info(
            "🔍 COMPARISON DEBUG: Computed remainingTrialDays (client calculation): \(computed)",
            service: serviceName)
        logger.info("🔍 COMPARISON DEBUG: Values match: \(stored == computed)", service: serviceName)

        if stored != computed {
            logger.warning(
                "🔍 COMPARISON DEBUG: DIFFERENCE DETECTED! Server/Stored (\(stored)) != Client Computed (\(computed))",
                service: serviceName)
            logger.info(
                "🔍 COMPARISON DEBUG: This difference is expected if server-provided days are being used",
                service: serviceName)
        }

        // ADDITIONAL DEBUG: Check total trial duration
        if let startDate = trialStartDate, let endDate = trialEndDate {
            let totalTrialInterval = endDate.timeIntervalSince(startDate)
            let totalTrialDays = totalTrialInterval / (24 * 3600)
            let calendar = Calendar.current
            let totalTrialDaysComponents =
                calendar.dateComponents([.day], from: startDate, to: endDate).day ?? 0

            logger.info(
                "🔍 TRIAL DURATION DEBUG: Total trial interval: \(totalTrialInterval) seconds",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DURATION DEBUG: Total trial days (decimal): \(totalTrialDays)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DURATION DEBUG: Total trial days (calendar): \(totalTrialDaysComponents)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DURATION DEBUG: Expected duration: \(trialDurationDays) days",
                service: serviceName)

            if totalTrialDaysComponents != trialDurationDays {
                logger.warning(
                    "🔍 TRIAL DURATION DEBUG: DURATION MISMATCH! Expected \(trialDurationDays), got \(totalTrialDaysComponents)",
                    service: serviceName)
            }
        }
    }

    private init() {
        logger.info("Initializing LicenseManager", service: serviceName)

        // Check if license system is disabled
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: All features available without restrictions",
                service: serviceName)
            // Set status to valid to ensure hasFullAccess returns true
            licenseStatus = .valid
            // Create a placeholder license info for UI display
            licenseInfo = LicenseInfo(
                licenseType: "Full Access",
                registeredUser: "Licensed User",
                email: "<EMAIL>",
                expirationDate: nil,
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ]
            )
            return  // Skip all license-related initialization
        }

        loadPersistedLicense()

        // Note: Automatic trial initialization removed - users must explicitly request trials
        // initializeTrialIfNeeded() // DISABLED: No automatic trial activation

        // Update trial status (for existing trials only)
        updateTrialStatus()

        // Debug comparison after initial update
        debugTrialDaysComparison()

        // Validate license on startup if we have one
        if !licenseKey.isEmpty {
            Task {
                await validateLicense(silent: true)
            }
        }
    }

    // MARK: - Public Methods

    /// Validate the current license key
    @MainActor
    func validateLicense(silent: Bool = false) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Skipping license validation", service: serviceName)
            return
        }

        guard !licenseKey.isEmpty else {
            if !silent {
                lastError = "Please enter a license key"
            }
            return
        }

        isValidating = true
        lastError = nil

        logger.info("Validating license key: \(licenseKey.prefix(8))...", service: serviceName)

        do {
            // Use new comprehensive API validation
            logger.info(
                "Using new API validation for license: \(licenseKey.prefix(8))...",
                service: serviceName)

            // Clean the license key for API validation (remove dashes)
            let cleanedKey = licenseKey.replacingOccurrences(of: "-", with: "")
            let response = try await LicenseAPIService.shared.validateLicenseAndRegisterDevice(
                licenseKey: cleanedKey)

            if response.valid {
                // Determine license status based on license type and expiration
                let licenseType = response.effectiveLicenseType ?? "License"
                let expirationDate = response.effectiveExpiresAt
                let email = response.effectiveEmail ?? userEmail

                // Set license status based on type and expiration
                if licenseType.lowercased() == "trial" {
                    licenseStatus = .trial
                    isTrialActive = true
                    trialEndDate = expirationDate
                    trialStartDate = response.effectiveCreatedAt ?? Date()

                    // 🚀 FIXED: Use server-provided dates for consistent calculation
                    // Calculate remaining days using the authoritative server dates
                    if let serverStartDate = trialStartDate, let serverEndDate = trialEndDate {
                        remainingTrialDays = calculateRemainingTrialDays(
                            startDate: serverStartDate, endDate: serverEndDate)
                        logger.info(
                            "🔍 VALIDATION SERVER DAYS DEBUG: Calculated \(remainingTrialDays) days using server dates",
                            service: serviceName)
                    } else {
                        logger.warning(
                            "🔍 VALIDATION SERVER DAYS DEBUG: Missing server dates, falling back to updateTrialStatus()",
                            service: serviceName)
                        updateTrialStatus()
                    }

                    // Log server-provided value for comparison if available
                    if let serverTrialDays = response.effectiveTrialDaysRemaining {
                        logger.info(
                            "🔍 VALIDATION SERVER DAYS DEBUG: Server also provided \(serverTrialDays) days for comparison",
                            service: serviceName)
                    }
                } else {
                    licenseStatus = .valid
                }

                // Create license info from new API response with backward compatibility
                licenseInfo = LicenseInfo(
                    licenseType: licenseType,
                    registeredUser: email.isEmpty ? "Licensed User" : email,
                    email: email.isEmpty ? "Licensed User" : email,
                    expirationDate: expirationDate,
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                )

                // Store additional license metadata if available from new API structure
                if let licenseId = response.effectiveLicenseId {
                    UserDefaults.standard.set(licenseId, forKey: "SnapbackLicenseId")
                    logger.info("License ID stored: \(licenseId)", service: serviceName)
                }

                if let createdAt = response.effectiveCreatedAt {
                    UserDefaults.standard.set(createdAt, forKey: "SnapbackLicenseCreatedAt")
                }

                if let updatedAt = response.effectiveUpdatedAt {
                    UserDefaults.standard.set(updatedAt, forKey: "SnapbackLicenseUpdatedAt")
                }

                // Store device token if provided (for future API calls)
                if let deviceToken = response.deviceToken {
                    UserDefaults.standard.set(deviceToken, forKey: "SnapbackDeviceToken")
                    logger.info("Device token stored for future API calls", service: serviceName)
                }

                // Update email if provided in response
                if !email.isEmpty && email != userEmail {
                    userEmail = email
                }

                logger.info(
                    "License validation successful - type: \(licenseType), devices: \(response.effectiveDevicesUsed ?? 0)/\(response.effectiveMaxDevices ?? 0)",
                    service: serviceName)

                // Log trial info if available
                if let trialInfo = response.trialInfo {
                    logger.info(
                        "Trial info - emailSent: \(trialInfo.emailSent), immediateAccess: \(trialInfo.immediateAccess)",
                        service: serviceName)
                }
            } else {
                licenseStatus = .invalid
                licenseInfo = nil
                lastError = response.message ?? "Invalid license key"
                logger.warning(
                    "License validation failed: \(response.message ?? "invalid key")",
                    service: serviceName)
            }

            // Persist the results
            persistLicense()

            // Notify about license status change
            postLicenseStatusChangeNotification()

        } catch {
            licenseStatus = .invalid
            licenseInfo = nil
            lastError = error.localizedDescription
            logger.error("License validation error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Set a new license key and validate it
    @MainActor
    func setLicenseKey(_ key: String) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Ignoring license key input", service: serviceName)
            return
        }

        let cleanKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        licenseKey = formatLicenseKeyForDisplay(cleanKey)

        if !cleanKey.isEmpty {
            await validateLicense()
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Set and validate a license key with email
    @MainActor
    func setLicenseKeyAndEmail(_ key: String, email: String) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Ignoring license key and email input",
                service: serviceName)
            return
        }

        let trimmedKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        logger.info(
            "Setting license key and email: \(trimmedKey.prefix(8))... for \(trimmedEmail)",
            service: serviceName)

        // Validate email format
        guard isValidEmail(trimmedEmail) else {
            lastError = "Please enter a valid email address"
            return
        }

        // Clear any previous error
        lastError = nil

        // Update the key and email (format the key for display)
        licenseKey = formatLicenseKeyForDisplay(trimmedKey)
        userEmail = trimmedEmail

        if !trimmedKey.isEmpty {
            await validateLicense()
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Clear the current license
    func clearLicense() {
        logger.info("Clearing license", service: serviceName)

        // Clear license-related properties
        licenseKey = ""
        userEmail = ""
        licenseStatus = .unlicensed
        licenseInfo = nil
        lastError = nil

        // Clear trial-related properties
        trialStartDate = nil
        trialEndDate = nil
        isTrialActive = false
        remainingTrialDays = 0

        // Clear from UserDefaults - both license and trial data
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastValidation)

        // Clear trial-related UserDefaults
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.firstLaunchDate)

        // Clear device token
        UserDefaults.standard.removeObject(forKey: "SnapbackDeviceToken")

        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 LICENSE CLEAR DEBUG: All license and trial data cleared", service: serviceName)

        // Notify about license status change
        postLicenseStatusChangeNotification()
    }

    // MARK: - Trial Management

    /// Initialize trial if this is the first launch and no license exists
    private func initializeTrialIfNeeded() {
        // For testing purposes, always start a fresh trial
        // This creates an active 15-day trial account for testing
        if licenseStatus == .unlicensed && licenseKey.isEmpty {
            // Clear any existing trial data to start fresh
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.firstLaunchDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)

            // Record first launch as now
            let now = Date()
            UserDefaults.standard.set(now, forKey: PersistenceKeys.firstLaunchDate)
            logger.info("Testing trial initialized, recorded date: \(now)", service: serviceName)

            // Start fresh trial
            startTrial()
        }
    }

    /// Request a trial license from the API using the provided email
    @MainActor
    func requestTrialLicense(email: String) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Ignoring trial license request", service: serviceName)
            return
        }

        logger.info("Requesting trial license for email: \(email)", service: serviceName)

        // Validate email format
        guard isValidEmail(email) else {
            lastError = "Please enter a valid email address"
            return
        }

        // Clear any previous error and set loading state
        lastError = nil
        isValidating = true

        do {
            // Request trial license using new API
            let response = try await LicenseAPIService.shared.createLicense(
                email: email, licenseType: "trial")

            // COMPREHENSIVE DEBUG: Log raw API response dates and server-calculated days
            logger.info("🔍 API RESPONSE DEBUG: Raw response received", service: serviceName)
            logger.info(
                "🔍 API RESPONSE DEBUG: effectiveCreatedAt: \(response.effectiveCreatedAt?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 API RESPONSE DEBUG: effectiveExpiresAt: \(response.effectiveExpiresAt?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 API RESPONSE DEBUG: effectiveLicenseKey: \(response.effectiveLicenseKey ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 API RESPONSE DEBUG: effectiveLicenseType: \(response.effectiveLicenseType ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 API RESPONSE DEBUG: effectiveTrialDaysRemaining: \(response.effectiveTrialDaysRemaining?.description ?? "nil")",
                service: serviceName)

            // Store the email and license key (format for display) using new response structure
            userEmail = email
            let licenseKey = response.effectiveLicenseKey ?? ""
            self.licenseKey = formatLicenseKeyForDisplay(licenseKey)

            // Set up trial state using new response structure
            let now = Date()
            let apiStartDate = response.effectiveCreatedAt
            let apiEndDate = response.effectiveExpiresAt

            // COMPREHENSIVE DEBUG: Log date processing
            logger.info("🔍 DATE PROCESSING DEBUG: Current time (now): \(now)", service: serviceName)
            logger.info(
                "🔍 DATE PROCESSING DEBUG: API start date: \(apiStartDate?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 DATE PROCESSING DEBUG: API end date: \(apiEndDate?.description ?? "nil")",
                service: serviceName)

            trialStartDate = apiStartDate ?? now
            trialEndDate =
                apiEndDate ?? Calendar.current.date(
                    byAdding: .day, value: trialDurationDays, to: now)!

            // COMPREHENSIVE DEBUG: Log final assigned dates
            logger.info(
                "🔍 FINAL DATES DEBUG: Final trialStartDate: \(trialStartDate!)",
                service: serviceName)
            logger.info(
                "🔍 FINAL DATES DEBUG: Final trialEndDate: \(trialEndDate!)", service: serviceName)

            // 🚀 FIXED: Use server-provided dates for consistent calculation
            // Calculate remaining days using the authoritative server dates
            if let serverStartDate = trialStartDate, let serverEndDate = trialEndDate {
                remainingTrialDays = calculateRemainingTrialDays(
                    startDate: serverStartDate, endDate: serverEndDate)
                logger.info(
                    "🔍 SERVER DAYS DEBUG: Calculated \(remainingTrialDays) days using server dates",
                    service: serviceName)
            } else {
                logger.warning(
                    "🔍 SERVER DAYS DEBUG: Missing server dates, falling back to updateTrialStatus()",
                    service: serviceName)
                updateTrialStatus()
            }

            // Log server-provided value for comparison if available
            if let serverTrialDays = response.effectiveTrialDaysRemaining {
                logger.info(
                    "🔍 SERVER DAYS DEBUG: Server also provided \(serverTrialDays) days for comparison",
                    service: serviceName)
            }

            licenseStatus = .trial
            isTrialActive = true

            // DIAGNOSTIC: Log date parsing details
            logger.info("🔍 TRIAL DATE DEBUG: API response dates:", service: serviceName)
            logger.info(
                "🔍 TRIAL DATE DEBUG: effectiveCreatedAt: \(response.effectiveCreatedAt?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DATE DEBUG: effectiveExpiresAt: \(response.effectiveExpiresAt?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DATE DEBUG: Final trialStartDate: \(trialStartDate?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 TRIAL DATE DEBUG: Final trialEndDate: \(trialEndDate?.description ?? "nil")",
                service: serviceName)

            // Store additional license metadata if available from new API structure
            if let licenseId = response.effectiveLicenseId {
                UserDefaults.standard.set(licenseId, forKey: "SnapbackLicenseId")
                logger.info("Trial license ID stored: \(licenseId)", service: serviceName)
            }

            // Create license info for trial using new response structure
            licenseInfo = LicenseInfo(
                licenseType: response.effectiveLicenseType ?? "Trial",
                registeredUser: email,
                email: email,
                expirationDate: trialEndDate,
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ]
            )

            // Log trial info if available
            if let trialInfo = response.trialInfo {
                logger.info(
                    "Trial created - emailSent: \(trialInfo.emailSent), immediateAccess: \(trialInfo.immediateAccess), note: \(trialInfo.note)",
                    service: serviceName)
            }

            // Generate checksum for trial data integrity using actual trial dates
            let checksum = generateTrialChecksum(startDate: trialStartDate!, endDate: trialEndDate!)
            UserDefaults.standard.set(checksum, forKey: PersistenceKeys.trialChecksum)

            // DIAGNOSTIC: Log checksum generation
            logger.info(
                "🔍 TRIAL DATE DEBUG: Generated checksum using startDate: \(trialStartDate!), endDate: \(trialEndDate!)",
                service: serviceName)

            persistTrialData()
            persistLicense()

            // Update trial status to calculate remaining days
            updateTrialStatus()

            // DIAGNOSTIC: Log trial activation details
            logger.info(
                "🔍 TRIAL ACTIVATION DEBUG: Trial license activated successfully for \(email)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL ACTIVATION DEBUG: License key: \(licenseKey)", service: serviceName)
            logger.info(
                "🔍 TRIAL ACTIVATION DEBUG: Trial start: \(trialStartDate?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 TRIAL ACTIVATION DEBUG: Trial end: \(trialEndDate?.description ?? "nil")",
                service: serviceName)
            logger.info(
                "🔍 TRIAL ACTIVATION DEBUG: License status: \(licenseStatus)",
                service: serviceName)

            // Debug comparison to check for discrepancies
            debugTrialDaysComparison()

            // Notify about license status change
            postLicenseStatusChangeNotification()

        } catch {
            lastError = error.localizedDescription
            logger.error("Trial request network error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Legacy method for backward compatibility - now redirects to API-based flow
    func startTrial() {
        logger.warning(
            "startTrial() called - this method is deprecated. Use requestTrialLicense(email:) instead.",
            service: serviceName)

        // For backward compatibility, start a basic trial without email
        // This should only be used in emergency cases
        let now = Date()
        let endDate =
            Calendar.current.date(byAdding: .day, value: trialDurationDays, to: now) ?? now

        trialStartDate = now
        trialEndDate = endDate
        licenseStatus = .trial
        isTrialActive = true

        // Create basic license info for trial
        licenseInfo = LicenseInfo(
            licenseType: "Trial",
            registeredUser: "Trial User",
            email: "<EMAIL>",
            expirationDate: endDate,
            features: [
                "All Workspace Features", "Unlimited Workspaces",
                "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
            ]
        )

        // Generate checksum for trial data integrity
        let checksum = generateTrialChecksum(startDate: now, endDate: endDate)
        UserDefaults.standard.set(checksum, forKey: PersistenceKeys.trialChecksum)

        persistTrialData()
        persistLicense()
        postLicenseStatusChangeNotification()

        logger.info("Legacy trial started: \(trialDurationDays) days", service: serviceName)
    }

    /// Update trial status and remaining days (FALLBACK METHOD - server-provided days preferred)
    /// This method is now primarily used as a fallback when server doesn't provide trialDaysRemaining
    private func updateTrialStatus() {
        logger.info(
            "🔍 TRIAL STATUS DEBUG: updateTrialStatus() called (FALLBACK MODE)", service: serviceName
        )

        guard let startDate = trialStartDate,
            let endDate = trialEndDate
        else {
            logger.info(
                "🔍 TRIAL STATUS DEBUG: Missing trial dates - startDate: \(trialStartDate?.description ?? "nil"), endDate: \(trialEndDate?.description ?? "nil")",
                service: serviceName)
            isTrialActive = false
            remainingTrialDays = 0
            return
        }

        // Verify trial data integrity
        if !verifyTrialIntegrity(startDate: startDate, endDate: endDate) {
            logger.warning(
                "Trial data integrity check failed - resetting trial", service: serviceName)
            resetTrialData()
            return
        }

        let now = Date()

        // COMPREHENSIVE DEBUG: Log all date information before calculation
        logger.info(
            "🔍 TRIAL CALCULATION DEBUG: Starting trial days calculation", service: serviceName)
        logger.info("🔍 TRIAL CALCULATION DEBUG: Current time (now): \(now)", service: serviceName)
        logger.info(
            "🔍 TRIAL CALCULATION DEBUG: Trial start date: \(startDate)", service: serviceName)
        logger.info("🔍 TRIAL CALCULATION DEBUG: Trial end date: \(endDate)", service: serviceName)
        logger.info(
            "🔍 TRIAL CALCULATION DEBUG: Time until end: \(endDate.timeIntervalSince(now)) seconds",
            service: serviceName)
        logger.info(
            "🔍 TRIAL CALCULATION DEBUG: Is now <= endDate? \(now <= endDate)", service: serviceName)

        if now <= endDate {
            // Trial is still active
            isTrialActive = true
            let calendar = Calendar.current

            // FIX: Use inclusive day calculation - same logic as computedRemainingTrialDays
            let startOfToday = calendar.startOfDay(for: now)
            let startOfEndDay = calendar.startOfDay(for: endDate)
            let daysBetween =
                calendar.dateComponents([.day], from: startOfToday, to: startOfEndDay).day ?? 0

            // Add 1 to make it inclusive (if trial ends today, show 1 day remaining)
            let inclusiveDays = daysBetween + 1
            remainingTrialDays = max(0, inclusiveDays)

            // COMPREHENSIVE DEBUG: Log calculation details
            logger.info("🔍 TRIAL CALCULATION DEBUG: Calendar: \(calendar)", service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Calendar timezone: \(calendar.timeZone)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Days between start of days: \(daysBetween)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Inclusive days (daysBetween + 1): \(inclusiveDays)",
                service: serviceName)

            // DETAILED ANALYSIS: Check the exact time difference for comparison
            let timeInterval = endDate.timeIntervalSince(now)
            let totalHours = timeInterval / 3600
            let totalDays = timeInterval / (24 * 3600)
            let oldCalculatedDays = calendar.dateComponents([.day], from: now, to: endDate).day ?? 0

            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Time interval: \(timeInterval) seconds",
                service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Total hours: \(totalHours)", service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Total days (decimal): \(totalDays)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Old calculation (raw): \(oldCalculatedDays)",
                service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Start of today: \(startOfToday)", service: serviceName)
            logger.info(
                "🔍 TRIAL CALCULATION DEBUG: Start of end day: \(startOfEndDay)",
                service: serviceName)

            // DIAGNOSTIC: Log detailed trial calculation info
            logger.info("🔍 TRIAL DAYS DEBUG: Trial is active", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: Current date: \(now)", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: End date: \(endDate)", service: serviceName)
            logger.info(
                "🔍 TRIAL DAYS DEBUG: Days between calculation: \(daysBetween)", service: serviceName
            )
            logger.info(
                "🔍 TRIAL DAYS DEBUG: Calculated remaining days: \(remainingTrialDays)",
                service: serviceName)

            if licenseStatus != .valid {
                licenseStatus = .trial
                // Ensure trial license info is set
                if licenseInfo == nil {
                    licenseInfo = LicenseInfo(
                        licenseType: "Trial",
                        registeredUser: "Trial User",
                        email: "<EMAIL>",  // Placeholder - will be updated with real email
                        expirationDate: endDate,
                        features: [
                            "All Workspace Features", "Unlimited Workspaces",
                            "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                        ]
                    )
                }
            }
        } else {
            // Trial has expired
            isTrialActive = false
            remainingTrialDays = 0

            // DIAGNOSTIC: Log expired trial info
            logger.info("🔍 TRIAL DAYS DEBUG: Trial has expired", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: Current date: \(now)", service: serviceName)
            logger.info("🔍 TRIAL DAYS DEBUG: End date: \(endDate)", service: serviceName)
            logger.info(
                "🔍 TRIAL DAYS DEBUG: Time difference: \(now.timeIntervalSince(endDate)) seconds",
                service: serviceName)

            if licenseStatus == .trial {
                licenseStatus = .expired
                // Update license info to reflect expired status
                licenseInfo = LicenseInfo(
                    licenseType: "Expired Trial",
                    registeredUser: "Trial User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: endDate,
                    features: ["Trial Expired"]
                )
                logger.info("Trial expired on \(endDate)", service: serviceName)

                // Notify about trial expiration
                postLicenseStatusChangeNotification()
            }
        }

        // Debug comparison at the end of updateTrialStatus
        debugTrialDaysComparison()
    }

    /// Generate checksum for trial data integrity
    private func generateTrialChecksum(startDate: Date, endDate: Date) -> String {
        let data =
            "\(startDate.timeIntervalSince1970):\(endDate.timeIntervalSince1970):\(trialDurationDays)"
        return String(data.hashValue)
    }

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    /// Verify trial data integrity
    private func verifyTrialIntegrity(startDate: Date, endDate: Date) -> Bool {
        guard
            let storedChecksum = UserDefaults.standard.string(forKey: PersistenceKeys.trialChecksum)
        else {
            logger.info("🔍 TRIAL INTEGRITY DEBUG: No stored checksum found", service: serviceName)
            return false
        }

        let expectedChecksum = generateTrialChecksum(startDate: startDate, endDate: endDate)
        let isValid = storedChecksum == expectedChecksum

        logger.info(
            "🔍 TRIAL INTEGRITY DEBUG: Checksum verification - stored: \(storedChecksum), expected: \(expectedChecksum), valid: \(isValid)",
            service: serviceName)

        return isValid
    }

    /// Reset trial data (for debugging or integrity failures)
    func resetTrialData() {
        logger.warning(
            "🔍 RESET TRIAL DEBUG: resetTrialData() called - clearing all trial data",
            service: serviceName)
        logger.info(
            "🔍 RESET TRIAL DEBUG: Before reset - trialStartDate: \(trialStartDate?.description ?? "nil")",
            service: serviceName)
        logger.info(
            "🔍 RESET TRIAL DEBUG: Before reset - trialEndDate: \(trialEndDate?.description ?? "nil")",
            service: serviceName)
        logger.info(
            "🔍 RESET TRIAL DEBUG: Before reset - remainingTrialDays: \(remainingTrialDays)",
            service: serviceName)

        trialStartDate = nil
        trialEndDate = nil
        isTrialActive = false
        remainingTrialDays = 0

        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialStartDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialEndDate)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.trialChecksum)
        UserDefaults.standard.synchronize()
    }

    /// Persist trial data to UserDefaults
    private func persistTrialData() {
        if let startDate = trialStartDate {
            UserDefaults.standard.set(startDate, forKey: PersistenceKeys.trialStartDate)
        }
        if let endDate = trialEndDate {
            UserDefaults.standard.set(endDate, forKey: PersistenceKeys.trialEndDate)
        }
        UserDefaults.standard.synchronize()
    }

    /// Post notification about license status change
    private func postLicenseStatusChangeNotification() {
        NotificationCenter.default.post(
            name: NSNotification.Name("LicenseStatusChanged"),
            object: licenseStatus
        )
    }

    // MARK: - Private Methods

    /// Format license key for display
    /// Preserves trial key format (TRIAL-timestamp-suffix)
    /// Formats regular keys as XXXX-XXXX-XXXX-XXXX
    private func formatLicenseKeyForDisplay(_ key: String) -> String {
        // Don't reformat trial keys - they have their own format
        if key.hasPrefix("TRIAL-") {
            return key
        }

        // Remove existing dashes and convert to uppercase
        let cleaned = key.replacingOccurrences(of: "-", with: "").uppercased()

        // Format as XXXX-XXXX-XXXX-XXXX for regular keys
        var formatted = ""
        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }

    /// Load persisted license information
    private func loadPersistedLicense() {
        licenseKey = UserDefaults.standard.string(forKey: PersistenceKeys.licenseKey) ?? ""
        userEmail = UserDefaults.standard.string(forKey: PersistenceKeys.userEmail) ?? ""

        if let statusRaw = UserDefaults.standard.string(forKey: PersistenceKeys.licenseStatus),
            let status = LicenseStatus(rawValue: statusRaw)
        {
            licenseStatus = status
        }

        if let infoData = UserDefaults.standard.data(forKey: PersistenceKeys.licenseInfo) {
            do {
                let decoder = JSONDecoder()
                // Note: For persisted data, we use standard decoding since it was encoded locally
                // The flexible date decoding is primarily for API responses
                let info = try decoder.decode(LicenseInfo.self, from: infoData)
                licenseInfo = info
            } catch {
                logger.warning(
                    "Failed to decode persisted license info: \(error)", service: serviceName)
                // Clear corrupted data
                UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
            }
        }

        // Load trial data
        trialStartDate =
            UserDefaults.standard.object(forKey: PersistenceKeys.trialStartDate) as? Date
        trialEndDate = UserDefaults.standard.object(forKey: PersistenceKeys.trialEndDate) as? Date

        // DIAGNOSTIC: Log detailed license loading info
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Loaded persisted license - Status: \(licenseStatus)",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: License key: '\(licenseKey.isEmpty ? "EMPTY" : "SET (\(licenseKey.count) chars)")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: User email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Trial start: \(trialStartDate?.description ?? "nil")",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Trial end: \(trialEndDate?.description ?? "nil")",
            service: serviceName)
    }

    /// Persist license information to UserDefaults
    private func persistLicense() {
        UserDefaults.standard.set(licenseKey, forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.set(userEmail, forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.set(licenseStatus.rawValue, forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastValidation)

        if let info = licenseInfo,
            let infoData = try? JSONEncoder().encode(info)
        {
            UserDefaults.standard.set(infoData, forKey: PersistenceKeys.licenseInfo)
        } else {
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        }

        UserDefaults.standard.synchronize()

        // DIAGNOSTIC: Log license persistence
        logger.info("🔍 LICENSE PERSIST DEBUG: License information persisted", service: serviceName)
        logger.info("🔍 LICENSE PERSIST DEBUG: Status: \(licenseStatus)", service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Key: '\(licenseKey.isEmpty ? "EMPTY" : "SET")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
    }

    /// Perform the actual license validation (placeholder implementation)
    private func performLicenseValidation(_ key: String) async throws -> LicenseValidationResult {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second

        // Clean the key for comparison
        let cleanKey = key.replacingOccurrences(of: "-", with: "").lowercased()

        // Demo license keys for testing different license types
        switch cleanKey {
        case "snapback2025life":
            // Lifetime license - never expires
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Lifetime License",
                    registeredUser: "Licensed User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: nil,  // Lifetime license
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                ),
                message: "Lifetime license activated successfully"
            )

        case "snapback2025year":
            // Expiring license - valid for 1 year from now
            let expirationDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Annual License",
                    registeredUser: "Licensed User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: expirationDate,
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                ),
                message: "Annual license activated successfully"
            )

        case "testexpiredlic":
            // Demo key that simulates an expired paid license for testing
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Annual License",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),  // 30 days ago
                    features: ["License Expired"]
                ),
                message: "License has expired"
            )

        case "testexprtrial123":
            // Demo key that simulates an expired trial for testing
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Trial",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()),  // Yesterday
                    features: ["Trial Expired"]
                ),
                message: "Trial period has expired"
            )

        case "test2025expr":
            // Standard format expired license test key: TEST-2025-EXPR-IRED
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Expired Annual License",
                    registeredUser: "Test User",
                    email: "<EMAIL>",  // Placeholder - will be updated with real email
                    expirationDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),  // 30 days ago
                    features: ["License Expired"]
                ),
                message:
                    "Your license has expired. Please purchase a new license or enter a valid license key."
            )

        default:
            // Invalid license key
            return LicenseValidationResult(
                status: .invalid,
                info: nil,
                message: "Invalid license key. Please check your key and try again."
            )
        }
    }
}

// MARK: - Supporting Types

/// License status enumeration
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case trial = "trial"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .unlicensed: return "No License"
        case .trial: return "Trial Active"
        case .valid: return "Valid"
        case .invalid: return "Invalid"
        case .expired: return "Expired"
        }
    }

    var color: Color {
        switch self {
        case .unlicensed: return .secondary
        case .trial: return .blue
        case .valid: return .green
        case .invalid: return .red
        case .expired: return .orange
        }
    }
}

/// License information structure
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let email: String
    let expirationDate: Date?
    let features: [String]

    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return expirationDate < Date()
    }

    var expirationDisplayText: String {
        guard let expirationDate = expirationDate else { return "Never expires" }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return formatter.string(from: expirationDate)
    }

    /// Get stored device token for API authentication
    func getDeviceToken() -> String? {
        return UserDefaults.standard.string(forKey: "SnapbackDeviceToken")
    }

    /// Get stored license ID from new API structure
    func getLicenseId() -> String? {
        return UserDefaults.standard.string(forKey: "SnapbackLicenseId")
    }

    /// Get stored license creation date from new API structure
    func getCreatedAt() -> Date? {
        return UserDefaults.standard.object(forKey: "SnapbackLicenseCreatedAt") as? Date
    }

    /// Get stored license update date from new API structure
    func getUpdatedAt() -> Date? {
        return UserDefaults.standard.object(forKey: "SnapbackLicenseUpdatedAt") as? Date
    }
}

/// License validation result
struct LicenseValidationResult {
    let status: LicenseStatus
    let info: LicenseInfo?
    let message: String?
}
